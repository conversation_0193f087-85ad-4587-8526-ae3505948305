---
type: "always_apply"
---

# Go Test Organization Standards

## Test File Structure

This project follows the **standard Go testing conventions** for test file organization:

### 1. Co-located Test Files (Recommended ✅)
- Test files (`*_test.go`) are placed alongside their corresponding source files in the same package directory
- This allows tests to access both public and private (unexported) functions and variables
- Example structure:
  ```
  internal/service/activity_cashback/
  ├── activity_cashback_service.go
  ├── activity_cashback_service_test.go
  ├── task_management_service.go
  ├── task_management_service_test.go
  └── integration_test.go
  ```

### 2. Shared Test Infrastructure
- Common test utilities are centralized in `internal/test/`
- Contains: configuration, helpers, fixtures, mocks
- Structure:
  ```
  internal/test/
  ├── config.go          # Test configuration setup
  ├── helpers.go         # Common test helper functions
  ├── fixtures.go        # Test data factories
  ├── mocks.go          # Mock implementations
  └── database_test.go   # Database setup tests
  ```

### 3. Test Types and Naming

#### Unit Tests
- File pattern: `*_test.go`
- Function pattern: `func TestFunctionName(t *testing.T)`
- Example: `TestActivityCashbackCalculation`

#### Integration Tests
- File pattern: `integration_test.go` or `*_integration_test.go`
- Use build tags: `//go:build integration`
- Example: `TestActivityCashbackIntegration`

#### Benchmark Tests
- Function pattern: `func BenchmarkFunctionName(b *testing.B)`
- Example: `BenchmarkTaskProcessing`

### 4. Test Commands

#### Run All Tests
```bash
make test                    # Run all tests
make test-verbose           # Run with verbose output
make test-coverage          # Run with coverage report
```

#### Run Specific Test Suites
```bash
make test-unit              # Unit tests only
make test-integration       # Integration tests only
make activity-cashback-test # Activity cashback specific tests
```

#### Run Package-Specific Tests
```bash
go test -v ./internal/service/activity_cashback/...
go test -v ./cmd/jwt-generator
go test -v ./internal/test
```

### 5. Best Practices

#### DO ✅
- Keep test files alongside source files in the same package
- Use descriptive test function names
- Group related tests using subtests (`t.Run()`)
- Use table-driven tests for multiple test cases
- Utilize shared test infrastructure from `internal/test/`
- Write both unit and integration tests
- Use build tags for integration tests

#### DON'T ❌
- Move test files to separate directories (breaks Go conventions)
- Create a single `tests/` directory at project root
- Mix test types without proper organization
- Duplicate test setup code across packages

### 6. Test Infrastructure Usage

#### Using Test Helpers
```go
import "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"

func TestExample(t *testing.T) {
    config, fixtures, helper := test.SetupTestWithDB(t)
    defer test.TeardownTest()
    
    user := fixtures.CreateTestUser()
    helper.AssertNotNil(user)
}
```

#### Using Test Fixtures
```go
fixtures := test.NewTestFixtures()
user := fixtures.CreateTestUser()
agentLevel := fixtures.CreateTestAgentLevel()
```

### 7. Makefile Integration

The project's Makefile provides comprehensive test targets:
- `test` - Run all tests
- `test-verbose` - Verbose test output
- `test-coverage` - Generate coverage reports
- `test-unit` - Unit tests only
- `test-integration` - Integration tests only
- `activity-cashback-test` - Specific test suite

### 8. Why This Structure Works

1. **Go Standard**: Follows official Go testing conventions
2. **Tool Support**: Works seamlessly with `go test`, IDEs, and CI/CD
3. **Access Control**: Tests can access private package functions
4. **Discoverability**: Tests are easy to find next to source code
5. **Maintainability**: Changes to source code immediately show related tests

## Summary

The current test organization is **already optimal** and follows Go best practices. No restructuring is needed. Continue using this pattern for all new test files.
