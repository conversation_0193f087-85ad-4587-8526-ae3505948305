package reward

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
)

// RewardClaimResultService handles business logic for reward claim results
type RewardClaimResultService struct {
	repo *repo.RewardClaimResultRepository
}

// NewRewardClaimResultService creates a new service instance
func NewRewardClaimResultService() *RewardClaimResultService {
	return &RewardClaimResultService{
		repo: repo.NewRewardClaimResultRepository(),
	}
}

// CreateResult saves a new reward claim result
func (s *RewardClaimResultService) CreateResult(record *model.RewardClaimResultRecord) error {
	return s.repo.Create(record)
}

// GetResultByID retrieves a reward claim result by ID
func (s *RewardClaimResultService) GetResultByID(id uuid.UUID) (*model.RewardClaimResultRecord, error) {
	return s.repo.GetByID(id)
}

// GetUserResults retrieves all reward claim results for a user
func (s *RewardClaimResultService) GetUserResults(userID uuid.UUID, limit, offset int) ([]model.RewardClaimResultRecord, error) {
	return s.repo.GetByUserID(userID, limit, offset)
}

// GetUserSuccessfulResults retrieves successful reward claim results for a user
func (s *RewardClaimResultService) GetUserSuccessfulResults(userID uuid.UUID, limit, offset int) ([]model.RewardClaimResultRecord, error) {
	return s.repo.GetByUserIDAndResult(userID, model.RewardClaimResultSuccess, limit, offset)
}

// GetUserFailedResults retrieves failed reward claim results for a user
func (s *RewardClaimResultService) GetUserFailedResults(userID uuid.UUID, limit, offset int) ([]model.RewardClaimResultRecord, error) {
	return s.repo.GetByUserIDAndResult(userID, model.RewardClaimResultFailed, limit, offset)
}

// GetResultsByToken retrieves reward claim results by token
func (s *RewardClaimResultService) GetResultsByToken(token string, limit, offset int) ([]model.RewardClaimResultRecord, error) {
	return s.repo.GetByToken(token, limit, offset)
}

// GetResultsByChainID retrieves reward claim results by chain ID
func (s *RewardClaimResultService) GetResultsByChainID(chainID int, limit, offset int) ([]model.RewardClaimResultRecord, error) {
	return s.repo.GetByChainID(chainID, limit, offset)
}

// GetResultsByDateRange retrieves reward claim results within a date range
func (s *RewardClaimResultService) GetResultsByDateRange(startDate, endDate time.Time, limit, offset int) ([]model.RewardClaimResultRecord, error) {
	return s.repo.GetByDateRange(startDate, endDate, limit, offset)
}

// GetFailedResults retrieves all failed reward claim results
func (s *RewardClaimResultService) GetFailedResults(limit, offset int) ([]model.RewardClaimResultRecord, error) {
	return s.repo.GetFailedClaims(limit, offset)
}

// GetSuccessfulResults retrieves all successful reward claim results
func (s *RewardClaimResultService) GetSuccessfulResults(limit, offset int) ([]model.RewardClaimResultRecord, error) {
	return s.repo.GetSuccessfulClaims(limit, offset)
}

// GetUserTotalAmountByToken calculates total amount claimed by user and token
func (s *RewardClaimResultService) GetUserTotalAmountByToken(userID uuid.UUID, token string) (decimal.Decimal, error) {
	return s.repo.GetTotalAmountByUserAndToken(userID, token)
}

// GetUserTotalAmount calculates total amount claimed by user across all tokens
func (s *RewardClaimResultService) GetUserTotalAmount(userID uuid.UUID) (decimal.Decimal, error) {
	return s.repo.GetTotalAmountByUser(userID)
}

// GetUserResultCount counts total records for a user
func (s *RewardClaimResultService) GetUserResultCount(userID uuid.UUID) (int64, error) {
	return s.repo.CountByUserID(userID)
}

// GetResultCountByStatus counts total records by result status
func (s *RewardClaimResultService) GetResultCountByStatus(result model.RewardClaimResult) (int64, error) {
	return s.repo.CountByResult(result)
}

// GetUserStats retrieves comprehensive statistics for a user
func (s *RewardClaimResultService) GetUserStats(userID uuid.UUID) (*UserRewardClaimStats, error) {
	stats := &UserRewardClaimStats{
		UserID: userID,
	}

	// Get total amount
	totalAmount, err := s.repo.GetTotalAmountByUser(userID)
	if err != nil {
		return nil, err
	}
	stats.TotalAmount = totalAmount

	// Get counts
	totalCount, err := s.repo.CountByUserID(userID)
	if err != nil {
		return nil, err
	}
	stats.TotalCount = totalCount

	successCount, err := s.repo.CountByResult(model.RewardClaimResultSuccess)
	if err != nil {
		return nil, err
	}
	stats.SuccessCount = successCount

	failedCount, err := s.repo.CountByResult(model.RewardClaimResultFailed)
	if err != nil {
		return nil, err
	}
	stats.FailedCount = failedCount

	// Calculate success rate
	if totalCount > 0 {
		stats.SuccessRate = float64(successCount) / float64(totalCount) * 100
	}

	return stats, nil
}

// GetUserClaimHistory retrieves paginated reward claim history for a user with optional filtering
func (s *RewardClaimResultService) GetUserClaimHistory(userID uuid.UUID, pageSize, offset int, isCashback bool, result *model.RewardClaimResult) ([]model.RewardClaimResultRecord, int64, error) {
	// Get paginated records with filters
	records, err := s.repo.GetByUserIDWithFilters(userID, pageSize, offset, isCashback, result)
	if err != nil {
		return nil, 0, err
	}

	// Get total count with filters
	totalCount, err := s.repo.CountByUserIDWithFilters(userID, isCashback, result)
	if err != nil {
		return nil, 0, err
	}

	return records, totalCount, nil
}

// GetUserTotalClaimedUsd calculates total USD amount claimed by user from successful claims
func (s *RewardClaimResultService) GetUserTotalClaimedUsd(userID uuid.UUID) (decimal.Decimal, error) {
	return s.repo.GetTotalUsdAmountByUser(userID, false)
}

// UserRewardClaimStats represents comprehensive statistics for a user's reward claims
type UserRewardClaimStats struct {
	UserID       uuid.UUID       `json:"user_id"`
	TotalAmount  decimal.Decimal `json:"total_amount"`
	TotalCount   int64           `json:"total_count"`
	SuccessCount int64           `json:"success_count"`
	FailedCount  int64           `json:"failed_count"`
	SuccessRate  float64         `json:"success_rate"`
}
