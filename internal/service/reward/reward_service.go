package reward

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
)

type RewardDataService struct {
	userRepo            repo.InvitationRepo
	commissionRepo      transaction.CommissionLedgerRepositoryInterface
	affiliateRepo       transaction.AffiliateTransactionRepositoryInterface
	levelRepo           repo.LevelRepo
	transactionUserRepo transaction.UserRepositoryInterface
}

func NewRewardDataService() *RewardDataService {
	return &RewardDataService{
		userRepo:            &agent_referral.InvitationRepository{},
		commissionRepo:      transaction.NewCommissionLedgerRepository(),
		affiliateRepo:       transaction.NewAffiliateTransactionRepository(),
		levelRepo:           repo.NewLevelRepository(),
		transactionUserRepo: transaction.NewUserRepository(),
	}
}

// WithdrawalRecord represents a withdrawal record with type information
type WithdrawalRecord struct {
	Hash             string `json:"hash"`
	WithdrawalReward string `json:"withdrawal_reward"`
	Date             string `json:"date"`
	Type             string `json:"type"` // "contract" or "meme"
}

// WithdrawalRecordsResult represents the result of withdrawal records query
type WithdrawalRecordsResult struct {
	Records []WithdrawalRecord `json:"records"`
	Total   int64              `json:"total"`
}

// GetWithdrawalRecords retrieves withdrawal records for a user with pagination
func (s *RewardDataService) GetWithdrawalRecords(ctx context.Context, userID uuid.UUID, page, pageSize int) (*WithdrawalRecordsResult, error) {
	var allRecords []WithdrawalRecord
	var totalCount int64

	// Calculate offset
	offset := (page - 1) * pageSize

	// Get contract withdrawal records (CommissionLedger with CLAIMED status)
	contractRecords, contractTotal, err := s.getContractWithdrawalRecords(ctx, userID, pageSize, offset)
	if err != nil {
		global.GVA_LOG.Error("Failed to get contract withdrawal records", zap.Error(err))
		return nil, fmt.Errorf("failed to get contract withdrawal records: %w", err)
	}

	// Get meme withdrawal records (MemeCommissionLedger with CLAIMED status)
	memeRecords, memeTotal, err := s.getMemeWithdrawalRecords(ctx, userID, pageSize, offset)
	if err != nil {
		global.GVA_LOG.Error("Failed to get meme withdrawal records", zap.Error(err))
		return nil, fmt.Errorf("failed to get meme withdrawal records: %w", err)
	}

	// Combine all records
	allRecords = append(allRecords, contractRecords...)
	allRecords = append(allRecords, memeRecords...)

	// Calculate total
	totalCount = contractTotal + memeTotal

	return &WithdrawalRecordsResult{
		Records: allRecords,
		Total:   totalCount,
	}, nil
}

// getContractWithdrawalRecords retrieves contract withdrawal records from CommissionLedger
func (s *RewardDataService) getContractWithdrawalRecords(ctx context.Context, userID uuid.UUID, pageSize, offset int) ([]WithdrawalRecord, int64, error) {
	var records []WithdrawalRecord
	var total int64

	// Query contract withdrawal records with transaction hash
	var result []struct {
		ID                  uuid.UUID       `json:"id"`
		CommissionAmount    decimal.Decimal `json:"commission_amount"`
		ClaimedAt           *time.Time      `json:"claimed_at"`
		TransactionHash     *string         `json:"transaction_hash"`
		SourceTransactionID string          `json:"source_transaction_id"`
	}

	// Get total count
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count contract withdrawal records: %w", err)
	}

	// Get paginated data with transaction hash
	err = global.GVA_DB.WithContext(ctx).
		Table("commission_ledger cl").
		Select(`
			cl.id,
			cl.commission_amount,
			cl.claimed_at,
			hlt.transaction_hash as transaction_hash,
			cl.source_transaction_id
		`).
		Joins("LEFT JOIN reward_claim_results hlt ON hlt.user_id = cl.recipient_user_id").
		Where("cl.recipient_user_id = ? AND cl.status = ?", userID, "CLAIMED").
		Order("cl.claimed_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get contract withdrawal records: %w", err)
	}

	// Convert to WithdrawalRecord format
	for _, item := range result {
		hash := ""
		if item.TransactionHash != nil && *item.TransactionHash != "" {
			hash = *item.TransactionHash
		}

		var date string
		if item.ClaimedAt != nil {
			date = item.ClaimedAt.Format("01-02")
		} else {
			date = "N/A"
		}

		records = append(records, WithdrawalRecord{
			Hash:             hash,
			WithdrawalReward: item.CommissionAmount.String(),
			Date:             date,
			Type:             "contract",
		})
	}

	return records, total, nil
}

// getMemeWithdrawalRecords retrieves meme withdrawal records from MemeCommissionLedger
func (s *RewardDataService) getMemeWithdrawalRecords(ctx context.Context, userID uuid.UUID, pageSize, offset int) ([]WithdrawalRecord, int64, error) {
	var records []WithdrawalRecord
	var total int64

	// Query meme withdrawal records with transaction hash
	var result []struct {
		ID                  uuid.UUID       `json:"id"`
		CommissionAmount    decimal.Decimal `json:"commission_amount"`
		CommissionAmountSol decimal.Decimal `json:"commission_amount_sol"`
		ClaimedAt           *time.Time      `json:"claimed_at"`
		TransactionHash     *string         `json:"transaction_hash"`
		SourceTransactionID string          `json:"source_transaction_id"`
	}

	// Get total count
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count meme withdrawal records: %w", err)
	}

	// Get paginated data with transaction hash
	err = global.GVA_DB.WithContext(ctx).
		Table("meme_commission_ledger mcl").
		Select(`
			mcl.id,
			mcl.commission_amount,
			mcl.commission_amount_sol,
			mcl.claimed_at,
			at.transaction_hash as transaction_hash,
			mcl.source_transaction_id
		`).
		Joins("LEFT JOIN reward_claim_results at ON at.user_id = mcl.recipient_user_id").
		Where("mcl.recipient_user_id = ? AND mcl.status = ?", userID, "CLAIMED").
		Order("mcl.claimed_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get meme withdrawal records: %w", err)
	}

	// Convert to WithdrawalRecord format
	for _, item := range result {
		hash := ""
		if item.TransactionHash != nil && *item.TransactionHash != "" {
			hash = *item.TransactionHash
		}

		var date string
		if item.ClaimedAt != nil {
			date = item.ClaimedAt.Format("01-02")
		} else {
			date = "N/A"
		}

		withdrawalReward := item.CommissionAmount.String()

		records = append(records, WithdrawalRecord{
			Hash:             hash,
			WithdrawalReward: withdrawalReward,
			Date:             date,
			Type:             "meme",
		})
	}

	return records, total, nil
}

// GetInvitationRecords retrieves invitation records for a user with pagination
func (s *RewardDataService) GetInvitationRecords(ctx context.Context, userID uuid.UUID, page, pageSize int) (*InvitationRecordsResult, error) {
	// Get all three-level invitation users (L1, L2, L3)
	threeLevelUserIDs, err := s.transactionUserRepo.GetAllDownlineUsers(ctx, userID, 3)
	if err != nil {
		global.GVA_LOG.Error("Failed to get three-level invitation users", zap.Error(err))
		return nil, fmt.Errorf("failed to get three-level invitation users: %w", err)
	}

	if len(threeLevelUserIDs) == 0 {
		return &InvitationRecordsResult{
			Records: []InvitationRecord{},
			Total:   0,
		}, nil
	}

	// For invitation records, we need to merge data from two different tables
	// To ensure accurate pagination, we'll fetch a reasonable amount of data and paginate in memory
	// This is necessary because we need to merge records by recipient_user_id across different tables

	// Set a reasonable limit to avoid fetching too much data
	// This limit should be large enough to handle most use cases but not so large as to cause memory issues
	maxFetchLimit := 10000

	// Get contract invitation records for three-level users
	// 不仅要获取合约奖励，还要获取合约交易量
	contractRecords, _, err := s.getContractInvitationRecordsForUsers(ctx, userID, threeLevelUserIDs, maxFetchLimit, 0)
	if err != nil {
		global.GVA_LOG.Error("Failed to get contract invitation records", zap.Error(err))
		return nil, fmt.Errorf("failed to get contract invitation records: %w", err)
	}

	// Get meme invitation records for three-level users
	// 不仅要获取meme奖励，还要获取meme交易量
	memeRecords, _, err := s.getMemeInvitationRecordsForUsers(ctx, userID, threeLevelUserIDs, maxFetchLimit, 0)
	if err != nil {
		global.GVA_LOG.Error("Failed to get meme invitation records", zap.Error(err))
		return nil, fmt.Errorf("failed to get meme invitation records: %w", err)
	}

	// Merge records by recipient_user_id
	mergedRecords := s.mergeInvitationRecords(contractRecords, memeRecords)

	// Calculate total unique recipient_user_ids after merging
	totalCount := int64(len(mergedRecords))

	// Apply pagination to merged results
	offset := (page - 1) * pageSize
	start := offset
	end := offset + pageSize

	var paginatedRecords []InvitationRecord
	if start >= len(mergedRecords) {
		paginatedRecords = []InvitationRecord{}
	} else {
		if end > len(mergedRecords) {
			end = len(mergedRecords)
		}
		paginatedRecords = mergedRecords[start:end]
	}

	return &InvitationRecordsResult{
		Records: paginatedRecords,
		Total:   totalCount,
	}, nil
}

// InvitationRecord represents an invitation record with commission information
type InvitationRecord struct {
	Address           string          `json:"address"`            // 被邀请用户的ID (recipient_user_id)
	TransactionVolume decimal.Decimal `json:"transaction_volume"` // 总交易量
	InvitedWithdrawal decimal.Decimal `json:"invited_withdrawal"` // 总奖励
	Date              string          `json:"date"`               // 最新的创建日期 (两个表的created_at)
	ChainID           int             `json:"chain_id"`           // Chain ID (Arbitrum for contract, Solana for meme)
	Token             string          `json:"token"`
	ContractVolume    decimal.Decimal `json:"contract_volume"` // 合约交易量
	MemeVolume        decimal.Decimal `json:"meme_volume"`     // Meme交易量
}

// InvitationRecordsResult represents the result of invitation records query
type InvitationRecordsResult struct {
	Records []InvitationRecord `json:"records"`
	Total   int64              `json:"total"`
}

// getContractInvitationRecords retrieves contract invitation records from CommissionLedger
func (s *RewardDataService) getContractInvitationRecords(ctx context.Context, userID uuid.UUID, pageSize, offset int) ([]InvitationRecord, int64, error) {
	var records []InvitationRecord
	var total int64

	// Query contract invitation records grouped by source_user_id
	var result []struct {
		SourceUserID    uuid.UUID       `json:"source_user_id"`
		ContractReward  decimal.Decimal `json:"contract_reward"`
		CreatedAt       *time.Time      `json:"created_at"`
		CommissionAsset string          `json:"commission_asset"`
	}

	// Get total count - count unique source_user_ids
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COUNT(DISTINCT source_user_id)").
		Where("recipient_user_id = ?", userID).
		Scan(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count contract invitation records: %w", err)
	}

	// Get paginated data grouped by source_user_id
	err = global.GVA_DB.WithContext(ctx).
		Table("commission_ledger").
		Select(`
			source_user_id,
			SUM(commission_amount) as contract_reward,
			MAX(created_at) as created_at,
			commission_asset
		`).
		Where("recipient_user_id = ?", userID).
		Group("source_user_id, commission_asset").
		Order("MAX(created_at) DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get contract invitation records: %w", err)
	}

	// Convert to InvitationRecord format
	for _, item := range result {
		var date string
		if item.CreatedAt != nil {
			date = item.CreatedAt.Format("2006-01-02")
		} else {
			date = "N/A"
		}

		record := InvitationRecord{
			Address:           item.SourceUserID.String(),
			TransactionVolume: decimal.Zero,
			InvitedWithdrawal: decimal.Zero, // Contract records don't have meme rewards
			Date:              date,
			ChainID:           42161, // Arbitrum One chain ID
		}
		records = append(records, record)
	}

	return records, total, nil
}

// getMemeInvitationRecords retrieves meme invitation records from MemeCommissionLedger
func (s *RewardDataService) getMemeInvitationRecords(ctx context.Context, userID uuid.UUID, pageSize, offset int) ([]InvitationRecord, int64, error) {
	var records []InvitationRecord
	var total int64

	// Query meme invitation records grouped by source_user_id
	var result []struct {
		SourceUserID uuid.UUID       `json:"source_user_id"`
		MemeReward   decimal.Decimal `json:"meme_reward"`
		CreatedAt    *time.Time      `json:"created_at"`
	}

	// Get total count - count unique source_user_ids
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).
		Select("COUNT(DISTINCT source_user_id)").
		Where("recipient_user_id = ?", userID).
		Scan(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count meme invitation records: %w", err)
	}

	// Get paginated data grouped by source_user_id
	err = global.GVA_DB.WithContext(ctx).
		Table("meme_commission_ledger").
		Select(`
			source_user_id,
			SUM(commission_amount) as meme_reward,
			MAX(created_at) as created_at
		`).
		Where("recipient_user_id = ?", userID).
		Group("source_user_id").
		Order("MAX(created_at) DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get meme invitation records: %w", err)
	}

	// Convert to InvitationRecord format
	for _, item := range result {
		var date string
		if item.CreatedAt != nil {
			date = item.CreatedAt.Format("2006-01-02")
		} else {
			date = "N/A"
		}

		record := InvitationRecord{
			Address:           item.SourceUserID.String(),
			TransactionVolume: decimal.Zero, // Meme records don't have contract rewards
			InvitedWithdrawal: decimal.Zero,
			Date:              date,
			ChainID:           101, // Solana chain ID
		}
		records = append(records, record)
	}

	return records, total, nil
}

// mergeInvitationRecords merges contract and meme invitation records by recipient_user_id
func (s *RewardDataService) mergeInvitationRecords(contractRecords, memeRecords []InvitationRecord) []InvitationRecord {
	// Create a map to store merged records by address (recipient_user_id)
	// mergedMap := make(map[string]*InvitationRecord)
	var mergedRecords []InvitationRecord
	// Process contract records
	for _, record := range contractRecords {
		mergedRecords = append(mergedRecords, record)
		// if existing, exists := mergedMap[record.Address]; exists {
		// 	// Merge with existing record - 累加合约相关的数据
		// 	existing.ContractVolume += record.ContractVolume       // 累加合约交易量
		// 	existing.InvitedWithdrawal += record.InvitedWithdrawal // 累加合约奖励
		// 	// Keep the latest date
		// 	if record.Date > existing.Date {
		// 		existing.Date = record.Date
		// 	}
		// 	// 如果现有记录是meme类型，需要更新ChainID为混合类型或保持合约类型
		// 	if existing.ChainID == int(utils.ChainIDSolanaInt) {
		// 		existing.ChainID = int(utils.ChainIDArbitrumOneInt) // 优先显示合约链ID
		// 	}
		// } else {
		// 	// Create new record
		// 	newRecord := record
		// 	// 设置总交易量为合约交易量
		// 	newRecord.TransactionVolume = record.ContractVolume
		// 	mergedMap[record.Address] = &newRecord
		// }
	}

	// Process meme records
	for _, record := range memeRecords {
		mergedRecords = append(mergedRecords, record)
		// if existing, exists := mergedMap[record.Address]; exists {
		// 	// Merge with existing record - 累加meme相关的数据
		// 	existing.MemeVolume += record.MemeVolume               // 累加meme交易量
		// 	existing.InvitedWithdrawal += record.InvitedWithdrawal // 累加meme奖励
		// 	// 更新总交易量 = 合约交易量 + meme交易量
		// 	existing.TransactionVolume = existing.ContractVolume + existing.MemeVolume
		// 	// Keep the latest date
		// 	if record.Date > existing.Date {
		// 		existing.Date = record.Date
		// 	}
		// 	// 如果现有记录是合约类型，保持合约链ID，否则更新为meme链ID
		// 	if existing.ChainID != int(utils.ChainIDArbitrumOneInt) {
		// 		existing.ChainID = int(utils.ChainIDSolanaInt)
		// 	}
		// 	// 更新Token字段（如果有的话）
		// 	if record.Token != "" {
		// 		existing.Token = record.Token
		// 	}
		// } else {
		// 	// Create new record
		// 	newRecord := record
		// 	// 设置总交易量为meme交易量
		// 	newRecord.TransactionVolume = record.MemeVolume
		// 	mergedMap[record.Address] = &newRecord
		// }
	}

	// Convert map back to slice and sort by date (latest first)
	// var mergedRecords []InvitationRecord
	// for _, record := range mergedMap {
	// 	mergedRecords = append(mergedRecords, *record)
	// }

	// Sort by date (latest first)
	for i := 0; i < len(mergedRecords)-1; i++ {
		for j := i + 1; j < len(mergedRecords); j++ {
			if mergedRecords[i].Date < mergedRecords[j].Date {
				mergedRecords[i], mergedRecords[j] = mergedRecords[j], mergedRecords[i]
			}
		}
	}

	return mergedRecords
}

// getContractInvitationRecordsForUsers retrieves contract invitation records for specific users
func (s *RewardDataService) getContractInvitationRecordsForUsers(ctx context.Context, userID uuid.UUID, threeLevelUserIDs []uuid.UUID, pageSize, offset int) ([]InvitationRecord, int64, error) {
	var records []InvitationRecord
	var total int64

	if len(threeLevelUserIDs) == 0 {
		return records, 0, nil
	}

	// Query contract invitation records with transaction volumes in one SQL
	var result []struct {
		SourceUserID   uuid.UUID       `json:"source_user_id"`
		ContractReward decimal.Decimal `json:"contract_reward"`
		ContractVolume decimal.Decimal `json:"contract_volume"`
		MemeVolume     decimal.Decimal `json:"meme_volume"`
		CreatedAt      *time.Time      `json:"created_at"`
	}

	// Get total count - count unique source_user_ids
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COUNT(DISTINCT source_user_id)").
		Where("recipient_user_id = ? AND source_user_id IN ?", userID, threeLevelUserIDs).
		Scan(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count contract invitation records: %w", err)
	}

	// Single SQL query to get all data including transaction volumes
	query := `
		WITH contract_rewards AS (
			SELECT 
				cl.source_user_id,
				SUM(cl.commission_amount) as contract_reward,
				MAX(cl.created_at) as created_at
			FROM commission_ledger cl
			WHERE cl.recipient_user_id = ? AND cl.source_user_id IN ?
			GROUP BY cl.source_user_id
		),
		contract_volumes AS (
			SELECT 
				hlt.user_id,
				COALESCE(SUM(
					COALESCE(hlt.avg_price, 0) * COALESCE(CAST(hlt.total_sz AS DECIMAL), 0)
				), 0) as contract_volume
			FROM hyper_liquid_transactions hlt
			WHERE hlt.user_id IN ?
			AND hlt.status = 'filled'
			AND hlt.avg_price IS NOT NULL
			AND hlt.total_sz IS NOT NULL
			GROUP BY hlt.user_id
		),
		meme_volumes AS (
			SELECT 
				at.user_id,
				COALESCE(SUM(
					at.quote_amount * COALESCE(sps.price, 0)
				), 0) as meme_volume
			FROM affiliate_transactions at
			LEFT JOIN LATERAL (
				SELECT price 
				FROM sol_price_snapshots 
				WHERE timestamp <= at.created_at 
				ORDER BY timestamp DESC 
				LIMIT 1
			) sps ON true
			WHERE at.user_id IN ?
			AND at.status = 'Completed'
			GROUP BY at.user_id
		)
		SELECT 
			cr.source_user_id,
			cr.contract_reward,
			COALESCE(cv.contract_volume, 0) as contract_volume,
			COALESCE(mv.meme_volume, 0) as meme_volume,
			cr.created_at
		FROM contract_rewards cr
		LEFT JOIN contract_volumes cv ON cv.user_id = cr.source_user_id
		LEFT JOIN meme_volumes mv ON mv.user_id = cr.source_user_id
		ORDER BY cr.created_at DESC
		LIMIT ? OFFSET ?
	`

	err = global.GVA_DB.WithContext(ctx).Debug().
		Raw(query, userID, threeLevelUserIDs, threeLevelUserIDs, threeLevelUserIDs, pageSize, offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get contract invitation records: %w", err)
	}

	// Convert to InvitationRecord format
	for _, item := range result {
		var date string
		if item.CreatedAt != nil {
			date = item.CreatedAt.Format("2006-01-02")
		} else {
			date = "N/A"
		}

		record := InvitationRecord{
			Address:           item.SourceUserID.String(),
			TransactionVolume: item.ContractVolume, // 使用合约交易量作为总交易量
			InvitedWithdrawal: item.ContractReward, // 合约奖励作为邀请提现
			Date:              date,
			ChainID:           int(utils.ChainIDArbitrumOneInt), // Arbitrum One chain ID
			ContractVolume:    item.ContractVolume,
			MemeVolume:        item.MemeVolume,
		}
		records = append(records, record)
	}

	return records, total, nil
}

// getMemeInvitationRecordsForUsers retrieves meme invitation records for specific users
func (s *RewardDataService) getMemeInvitationRecordsForUsers(ctx context.Context, userID uuid.UUID, threeLevelUserIDs []uuid.UUID, pageSize, offset int) ([]InvitationRecord, int64, error) {
	var records []InvitationRecord
	var total int64

	if len(threeLevelUserIDs) == 0 {
		return records, 0, nil
	}

	// Query meme invitation records with transaction volumes in one SQL
	var result []struct {
		SourceUserID   uuid.UUID       `json:"source_user_id"`
		MemeReward     decimal.Decimal `json:"meme_reward"`
		ContractVolume decimal.Decimal `json:"contract_volume"`
		MemeVolume     decimal.Decimal `json:"meme_volume"`
		CreatedAt      *time.Time      `json:"created_at"`
	}

	// Get total count - count unique source_user_ids
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).
		Select("COUNT(DISTINCT source_user_id)").
		Where("recipient_user_id = ? AND source_user_id IN ?", userID, threeLevelUserIDs).
		Scan(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count meme invitation records: %w", err)
	}

	// Single SQL query to get all data including transaction volumes
	query := `
		WITH meme_rewards AS (
			SELECT 
				mcl.source_user_id,
				SUM(mcl.commission_amount) as meme_reward,
				MAX(mcl.created_at) as created_at
			FROM meme_commission_ledger mcl
			WHERE mcl.recipient_user_id = ? AND mcl.source_user_id IN ?
			GROUP BY mcl.source_user_id
		),
		contract_volumes AS (
			SELECT 
				hlt.user_id,
				COALESCE(SUM(
					COALESCE(hlt.avg_price, 0) * COALESCE(CAST(hlt.total_sz AS DECIMAL), 0)
				), 0) as contract_volume
			FROM hyper_liquid_transactions hlt
			WHERE hlt.user_id IN ?
			AND hlt.status = 'filled'
			AND hlt.avg_price IS NOT NULL
			AND hlt.total_sz IS NOT NULL
			GROUP BY hlt.user_id
		),
		meme_volumes AS (
			SELECT 
				at.user_id,
				COALESCE(SUM(
					at.quote_amount * COALESCE(sps.price, 0)
				), 0) as meme_volume
			FROM affiliate_transactions at
			LEFT JOIN LATERAL (
				SELECT price 
				FROM sol_price_snapshots 
				WHERE timestamp <= at.created_at 
				ORDER BY timestamp DESC 
				LIMIT 1
			) sps ON true
			WHERE at.user_id IN ?
			AND at.status = 'Completed'
			GROUP BY at.user_id
		)
		SELECT 
			mr.source_user_id,
			mr.meme_reward,
			COALESCE(cv.contract_volume, 0) as contract_volume,
			COALESCE(mv.meme_volume, 0) as meme_volume,
			mr.created_at
		FROM meme_rewards mr
		LEFT JOIN contract_volumes cv ON cv.user_id = mr.source_user_id
		LEFT JOIN meme_volumes mv ON mv.user_id = mr.source_user_id
		ORDER BY mr.created_at DESC
		LIMIT ? OFFSET ?
	`

	err = global.GVA_DB.WithContext(ctx).Debug().
		Raw(query, userID, threeLevelUserIDs, threeLevelUserIDs, threeLevelUserIDs, pageSize, offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get meme invitation records: %w", err)
	}

	// Convert to InvitationRecord format
	for _, item := range result {
		var date string
		if item.CreatedAt != nil {
			date = item.CreatedAt.Format("2006-01-02")
		} else {
			date = "N/A"
		}

		record := InvitationRecord{
			Address:           item.SourceUserID.String(),
			TransactionVolume: item.MemeVolume, // 使用meme交易量作为总交易量
			InvitedWithdrawal: item.MemeReward, // meme奖励作为邀请提现
			Date:              date,
			ChainID:           int(utils.ChainIDSolanaInt), // Solana chain ID
			ContractVolume:    item.ContractVolume,
			MemeVolume:        item.MemeVolume,
		}
		records = append(records, record)
	}

	return records, total, nil
}
