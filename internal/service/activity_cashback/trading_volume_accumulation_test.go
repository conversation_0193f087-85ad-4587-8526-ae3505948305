package activity_cashback

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

// TradingVolumeAccumulationTestSuite tests trading volume accumulation in user_tier_info
type TradingVolumeAccumulationTestSuite struct {
	suite.Suite
	ctx        context.Context
	service    ActivityCashbackServiceInterface
	testUserID uuid.UUID
	helper     *test.TestHelper
}

// SetupSuite initializes the test suite
func (suite *TradingVolumeAccumulationTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.testUserID = uuid.New()
	suite.helper = test.NewTestHelper(suite.T())

	// Setup test configuration and database
	test.SetupTestWithDB(suite.T())

	// Initialize service
	suite.service = NewActivityCashbackService()
}

// TearDownSuite cleans up after all tests
func (suite *TradingVolumeAccumulationTestSuite) TearDownSuite() {
	test.TeardownTest()
}

// TestTradingVolumeAccumulation_NewSystem tests the new system where trading volume is accumulated via Level Upgrade Task
func (suite *TradingVolumeAccumulationTestSuite) TestTradingVolumeAccumulation_NewSystem() {
	// This test demonstrates the new system: trading volume is accumulated via Level Upgrade Task from daily_meme_volumes

	// Arrange: Initialize user for activity cashback
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, suite.testUserID)
	suite.NoError(err)

	// Get initial tier info
	initialTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, suite.testUserID)
	suite.NoError(err)
	suite.Equal(decimal.Zero, initialTierInfo.TradingVolumeUSD, "Initial trading volume should be 0")

	// Act: Simulate multiple trades through the current system
	trades := []map[string]interface{}{
		{
			"trade_type": "MEME",
			"volume":     100.0,
			"order_id":   uuid.New().String(),
			"user_id":    suite.testUserID.String(),
		},
		{
			"trade_type": "MEME",
			"volume":     250.0,
			"order_id":   uuid.New().String(),
			"user_id":    suite.testUserID.String(),
		},
		{
			"trade_type": "MEME",
			"volume":     50.0,
			"order_id":   uuid.New().String(),
			"user_id":    suite.testUserID.String(),
		},
	}

	// Process trades through TaskManager (current flow)
	taskProcessorManager := NewTaskManager(suite.service)
	for _, tradeData := range trades {
		err := taskProcessorManager.ProcessTradingEvent(suite.ctx, suite.testUserID, tradeData)
		suite.NoError(err, "Trade processing should succeed")
	}

	// Assert: Check that trading volume is NOT accumulated via NATS (new behavior)
	finalTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, suite.testUserID)
	suite.NoError(err)

	suite.T().Logf("Trading volume after NATS processing: %s", finalTierInfo.TradingVolumeUSD.String())

	// Trading volume should still be 0 because NATS no longer accumulates volume
	suite.Equal(decimal.Zero, finalTierInfo.TradingVolumeUSD, "Trading volume should be 0 after NATS processing")

	// Check ActivityCashbackSummary
	summary, err := suite.service.GetActivityCashbackSummary(suite.ctx, suite.testUserID)
	suite.NoError(err)

	suite.T().Logf("Summary AccumulatedTradingVolumeUSD: %s", summary.AccumulatedTradingVolumeUSD.String())

	// Summary should also show 0 because volume accumulation is now handled by Level Upgrade Task
	suite.Equal(decimal.Zero, summary.AccumulatedTradingVolumeUSD, "Summary should show 0 until Level Upgrade Task runs")

	suite.T().Log("✅ NEW SYSTEM: Trading volume accumulation is now handled by Level Upgrade Task")
	suite.T().Log("✅ NATS events only process tasks, not volume accumulation")
}

// TestTradingVolumeAccumulation_ProposedFix tests the proposed fix
func (suite *TradingVolumeAccumulationTestSuite) TestTradingVolumeAccumulation_ProposedFix() {
	// This test demonstrates how the fix should work

	// Arrange: Initialize user
	testUserID := uuid.New()
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, testUserID)
	suite.NoError(err)

	// Get initial tier info
	initialTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
	suite.NoError(err)
	suite.Equal(decimal.Zero, initialTierInfo.TradingVolumeUSD)

	// Act: Manually add trading volume using the AddTradingVolume method
	// This is what should happen automatically when trades are processed
	volumes := []decimal.Decimal{
		decimal.NewFromFloat(100.0),
		decimal.NewFromFloat(250.0),
		decimal.NewFromFloat(50.0),
	}

	for _, volume := range volumes {
		// Get current tier info
		tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
		suite.NoError(err)

		// Add trading volume (this is what's missing in current implementation)
		tierInfo.AddTradingVolume(volume)

		// Update tier info
		err = suite.service.UpdateUserTierInfo(suite.ctx, tierInfo)
		suite.NoError(err)
	}

	// Assert: Check if trading volume was accumulated correctly
	finalTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
	suite.NoError(err)

	expectedVolume := decimal.NewFromFloat(400.0)
	suite.Equal(expectedVolume, finalTierInfo.TradingVolumeUSD, "Trading volume should be accumulated with the fix")

	// Check ActivityCashbackSummary
	summary, err := suite.service.GetActivityCashbackSummary(suite.ctx, testUserID)
	suite.NoError(err)

	suite.Equal(expectedVolume, summary.AccumulatedTradingVolumeUSD, "Summary should show accumulated volume")
	suite.T().Log("✅ With the fix, AccumulatedTradingVolumeUSD is correctly accumulated")
}

// TestTradingVolumeAccumulation_MEMEOnly tests volume accumulation with MEME trades only
func (suite *TradingVolumeAccumulationTestSuite) TestTradingVolumeAccumulation_MEMEOnly() {
	// Test that only MEME trades are processed for Activity Cashback

	testUserID := uuid.New()
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, testUserID)
	suite.NoError(err)

	// Test data with different trade types
	trades := []struct {
		tradeType     string
		volume        float64
		shouldProcess bool // Whether this trade should be processed
	}{
		{"MEME", 1000.0, true},         // MEME trades are processed
		{"PERPETUAL", 2000.0, false},   // Derivatives trades are excluded
		{"DERIVATIVES", 1000.0, false}, // Derivatives trades are excluded
	}

	expectedTotalVolume := decimal.Zero

	for _, trade := range trades {
		// Only MEME trades should be processed
		if trade.shouldProcess {
			expectedTotalVolume = expectedTotalVolume.Add(decimal.NewFromFloat(trade.volume))

			// Get current tier info
			tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
			suite.NoError(err)

			// Add trading volume (only MEME trades are processed)
			tierInfo.AddTradingVolume(decimal.NewFromFloat(trade.volume))

			// Update tier info
			err = suite.service.UpdateUserTierInfo(suite.ctx, tierInfo)
			suite.NoError(err)

			suite.T().Logf("Added %s trade: volume=%.2f (processed)",
				trade.tradeType, trade.volume)
		} else {
			suite.T().Logf("Skipped %s trade: volume=%.2f (excluded from Activity Cashback)",
				trade.tradeType, trade.volume)
		}
	}

	// Assert: Check final accumulated volume
	finalTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
	suite.NoError(err)

	suite.Equal(expectedTotalVolume, finalTierInfo.TradingVolumeUSD,
		"Only MEME trading volume should be accumulated")

	// Expected: Only MEME trade (1000.0) should be counted
	suite.Equal(decimal.NewFromFloat(1000.0), finalTierInfo.TradingVolumeUSD)

	suite.T().Log("✅ MEME-only volume accumulation works correctly")
}

// TestTradingVolumeAccumulation_RealWorldScenario tests with realistic small volumes
func (suite *TradingVolumeAccumulationTestSuite) TestTradingVolumeAccumulation_RealWorldScenario() {
	// Test with realistic small volumes like in the user's actual data

	testUserID := uuid.New()
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, testUserID)
	suite.NoError(err)

	// Realistic small volumes from actual NATS events
	realVolumes := []float64{
		0.002974233, // From affiliate_transactions example
		0.000032864, // From NATS event example
		0.001500000, // Another small trade
		0.000100000, // Very small trade
	}

	expectedTotal := decimal.Zero

	for _, volume := range realVolumes {
		// Get current tier info
		tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
		suite.NoError(err)

		// Add trading volume
		volumeDecimal := decimal.NewFromFloat(volume)
		tierInfo.AddTradingVolume(volumeDecimal)
		expectedTotal = expectedTotal.Add(volumeDecimal)

		// Update tier info
		err = suite.service.UpdateUserTierInfo(suite.ctx, tierInfo)
		suite.NoError(err)
	}

	// Assert: Check accumulated volume
	finalTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
	suite.NoError(err)

	suite.Equal(expectedTotal, finalTierInfo.TradingVolumeUSD,
		"Small volumes should be accumulated correctly")

	// Check that it's greater than 0
	suite.True(finalTierInfo.TradingVolumeUSD.GreaterThan(decimal.Zero),
		"Accumulated volume should be greater than 0")

	suite.T().Logf("✅ Real-world small volumes accumulated: %s USD",
		finalTierInfo.TradingVolumeUSD.String())
}

// TestLevelUpgradeTaskVolumeAccumulation tests the Level Upgrade Task volume accumulation functionality
func (suite *TradingVolumeAccumulationTestSuite) TestLevelUpgradeTaskVolumeAccumulation() {
	// This test simulates the Level Upgrade Task calculating accumulated volume from daily_meme_volumes

	testUserID := uuid.New()
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, testUserID)
	suite.NoError(err)

	// Simulate daily_meme_volumes data (this would normally be populated by the Level Upgrade Task's aggregation)
	// For testing, we'll manually insert some daily volume data
	dailyVolumes := []struct {
		date   time.Time
		volume decimal.Decimal
	}{
		{time.Now().AddDate(0, 0, -3), decimal.NewFromFloat(100.50)}, // 3 days ago
		{time.Now().AddDate(0, 0, -2), decimal.NewFromFloat(250.75)}, // 2 days ago
		{time.Now().AddDate(0, 0, -1), decimal.NewFromFloat(50.25)},  // yesterday
	}

	// Insert test data into daily_meme_volumes
	for _, dv := range dailyVolumes {
		dailyMemeVolume := model.DailyMemeVolume{
			UserID:        testUserID,
			Date:          dv.date,
			MemeVolumeUSD: dv.volume,
		}
		err := global.GVA_DB.Create(&dailyMemeVolume).Error
		suite.NoError(err, "Should be able to create daily meme volume record")
	}

	// Calculate expected total volume
	expectedTotal := decimal.NewFromFloat(401.50) // 100.50 + 250.75 + 50.25

	// Simulate what the Level Upgrade Task would do
	// Calculate SUM of all-time MEME volume for the user
	var calculatedTotal decimal.Decimal
	err = global.GVA_DB.Model(&model.DailyMemeVolume{}).
		Where("user_id = ?", testUserID).
		Select("COALESCE(SUM(meme_volume_usd), 0)").
		Scan(&calculatedTotal).Error
	suite.NoError(err)

	suite.Equal(expectedTotal, calculatedTotal, "Calculated total should match expected")

	// Update user_tier_info with the calculated volume (simulating Level Upgrade Task)
	var userTierInfo model.UserTierInfo
	result := global.GVA_DB.Where("user_id = ?", testUserID).First(&userTierInfo)
	suite.NoError(result.Error)

	// Update the trading volume
	err = global.GVA_DB.Model(&userTierInfo).Update("trading_volume_usd", calculatedTotal).Error
	suite.NoError(err)

	// Verify the update
	finalTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
	suite.NoError(err)

	suite.Equal(expectedTotal, finalTierInfo.TradingVolumeUSD, "User tier info should have accumulated volume")

	// Check ActivityCashbackSummary
	summary, err := suite.service.GetActivityCashbackSummary(suite.ctx, testUserID)
	suite.NoError(err)

	suite.Equal(expectedTotal, summary.AccumulatedTradingVolumeUSD, "Summary should show accumulated volume")

	suite.T().Logf("✅ Level Upgrade Task simulation successful: %s USD accumulated",
		finalTierInfo.TradingVolumeUSD.String())
}

// Run the test suite
func TestTradingVolumeAccumulationSuite(t *testing.T) {
	suite.Run(t, new(TradingVolumeAccumulationTestSuite))
}
