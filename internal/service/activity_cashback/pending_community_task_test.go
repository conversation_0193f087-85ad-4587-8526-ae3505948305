package activity_cashback

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

func TestPendingCommunityTaskFlow(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	ctx := context.Background()

	// Create test service
	service := NewActivityCashbackService()

	// Create test user
	userID := uuid.New()
	err := service.InitializeUserForActivityCashback(ctx, userID)
	require.NoError(t, err)

	// Create test community task
	taskRepo := activity_cashback.NewActivityTaskRepository()
	categoryRepo := activity_cashback.NewTaskCategoryRepository()

	// Create community category
	category := &model.TaskCategory{
		Name:        "community",
		DisplayName: "Community Tasks",
		IsActive:    true,
		SortOrder:   1,
	}
	err = categoryRepo.Create(ctx, category)
	require.NoError(t, err)

	// Create Twitter follow task
	verificationMethod := model.VerificationClickVerify
	taskIdentifier := model.TaskIDTwitterFollow
	task := &model.ActivityTask{
		ID:                 uuid.New(),
		CategoryID:         category.ID,
		Name:               "Follow Twitter",
		Description:        stringPtr("Follow us on Twitter"),
		Frequency:          model.FrequencyOneTime,
		Points:             50,
		TaskIdentifier:     &taskIdentifier,
		VerificationMethod: &verificationMethod,
		IsActive:           true,
	}
	err = taskRepo.Create(ctx, task)
	require.NoError(t, err)

	t.Run("Create pending community task", func(t *testing.T) {
		// Create pending task
		verificationData := map[string]interface{}{
			"clicked_at": time.Now().Unix(),
			"user_agent": "test-agent",
		}

		pendingTask, err := service.CreatePendingCommunityTask(ctx, userID, task.ID, verificationData)
		require.NoError(t, err)
		assert.NotNil(t, pendingTask)
		assert.Equal(t, userID, pendingTask.UserID)
		assert.Equal(t, task.ID, pendingTask.TaskID)
		assert.Equal(t, model.PendingCommunityTaskStatusPending, pendingTask.Status)
		assert.NotNil(t, pendingTask.CompletionTime)

		// Check remaining wait time is approximately 2 minutes (120 seconds)
		remainingTime := pendingTask.GetRemainingWaitTime()
		assert.True(t, remainingTime > 110 && remainingTime <= 120, "Remaining time should be around 120 seconds, got %d", remainingTime)
	})

	t.Run("Check has pending task", func(t *testing.T) {
		hasPending, err := service.HasPendingCommunityTask(ctx, userID, task.ID)
		require.NoError(t, err)
		assert.True(t, hasPending)
	})

	t.Run("Get pending task", func(t *testing.T) {
		pendingTask, err := service.GetPendingCommunityTask(ctx, userID, task.ID)
		require.NoError(t, err)
		assert.NotNil(t, pendingTask)
		assert.Equal(t, userID, pendingTask.UserID)
		assert.Equal(t, task.ID, pendingTask.TaskID)
	})

	t.Run("Cannot create duplicate pending task", func(t *testing.T) {
		verificationData := map[string]interface{}{
			"clicked_at": time.Now().Unix(),
		}

		_, err := service.CreatePendingCommunityTask(ctx, userID, task.ID, verificationData)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "already has a pending task")
	})

	t.Run("Process pending tasks before time", func(t *testing.T) {
		// Try to process before 2 minutes - should not complete anything
		err := service.ProcessPendingCommunityTasks(ctx)
		require.NoError(t, err)

		// Task should still be pending
		hasPending, err := service.HasPendingCommunityTask(ctx, userID, task.ID)
		require.NoError(t, err)
		assert.True(t, hasPending)
	})

	t.Run("Process pending tasks after time", func(t *testing.T) {
		// Get the pending task and manually set completion time to past
		pendingTaskRepo := activity_cashback.NewPendingCommunityTaskRepository()
		pendingTask, err := service.GetPendingCommunityTask(ctx, userID, task.ID)
		require.NoError(t, err)

		// Set completion time to 1 minute ago
		pastTime := time.Now().Add(-1 * time.Minute)
		pendingTask.CompletionTime = &pastTime
		err = pendingTaskRepo.Update(ctx, pendingTask)
		require.NoError(t, err)

		// Now process pending tasks - should complete the task
		err = service.ProcessPendingCommunityTasks(ctx)
		require.NoError(t, err)

		// Check that task is no longer pending (should return false for completed tasks)
		hasPending, err := service.HasPendingCommunityTask(ctx, userID, task.ID)
		require.NoError(t, err)

		// Debug: Check pending task status
		pendingTaskRepo2 := activity_cashback.NewPendingCommunityTaskRepository()
		allPendingTasks, _ := pendingTaskRepo2.GetPendingTasks(ctx)
		t.Logf("All pending tasks count: %d", len(allPendingTasks))

		// Get the specific pending task to check its status
		pendingTaskAfter, err2 := pendingTaskRepo2.GetByUserAndTask(ctx, userID, task.ID)
		if err2 == nil {
			t.Logf("Pending task status after processing: %s", pendingTaskAfter.Status)
		} else {
			t.Logf("Error getting pending task: %v", err2)
		}

		assert.False(t, hasPending, "Task should no longer be pending after completion")

		// Check that user task progress was completed
		// Refresh progress from database
		progressRepo := activity_cashback.NewUserTaskProgressRepository()
		progress, err := progressRepo.GetByUserAndTask(ctx, userID, task.ID)
		require.NoError(t, err)
		t.Logf("Progress status: %s, Points earned: %d", progress.Status, progress.PointsEarned)
		assert.Equal(t, model.TaskStatusCompleted, progress.Status, "Task status should be COMPLETED")
		assert.Equal(t, task.Points, progress.PointsEarned, "Points earned should match task points")
	})
}

func TestPendingCommunityTaskRepository(t *testing.T) {
	// Setup test database
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	ctx := context.Background()
	repo := activity_cashback.NewPendingCommunityTaskRepository()

	userID := uuid.New()
	taskID := uuid.New()

	t.Run("Create and retrieve pending task", func(t *testing.T) {
		pendingTask := &model.PendingCommunityTask{
			ID:        uuid.New(),
			UserID:    userID,
			TaskID:    taskID,
			Status:    model.PendingCommunityTaskStatusPending,
			ClickedAt: time.Now(),
		}

		err := repo.Create(ctx, pendingTask)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, pendingTask.ID)
		assert.NotNil(t, pendingTask.CompletionTime)

		// Retrieve by ID
		retrieved, err := repo.GetByID(ctx, pendingTask.ID)
		require.NoError(t, err)
		assert.Equal(t, pendingTask.ID, retrieved.ID)
		assert.Equal(t, userID, retrieved.UserID)
		assert.Equal(t, taskID, retrieved.TaskID)
	})

	t.Run("Get ready for completion", func(t *testing.T) {
		// Create a task that's ready for completion
		pastTime := time.Now().Add(-1 * time.Minute)
		readyTask := &model.PendingCommunityTask{
			ID:        uuid.New(),
			UserID:    uuid.New(),
			TaskID:    uuid.New(),
			Status:    model.PendingCommunityTaskStatusPending,
			ClickedAt: pastTime,
		}

		// Manually set completion time to past (repo.Create will override it)
		err := repo.Create(ctx, readyTask)
		require.NoError(t, err)

		// Update completion time to past manually
		readyTask.CompletionTime = &pastTime
		err = repo.Update(ctx, readyTask)
		require.NoError(t, err)

		// Get ready tasks
		readyTasks, err := repo.GetReadyForCompletion(ctx)
		require.NoError(t, err)
		assert.True(t, len(readyTasks) > 0, "Should have at least one ready task")

		// Find our task in the results
		found := false
		for _, task := range readyTasks {
			if task.ID == readyTask.ID {
				found = true
				assert.True(t, task.IsReadyForCompletion(), "Task should be ready for completion")
				break
			}
		}
		assert.True(t, found, "Ready task should be found in results")
	})
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}
