package activity_cashback

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func TestTaskRegistry_RegisterHandler(t *testing.T) {
	mockService := &MockActivityCashbackService{}
	registry := NewTaskRegistry(mockService)

	// Create a test handler
	handler := NewDailyCheckinHandler(mockService)

	// Register the handler
	registry.RegisterHandler(handler)

	// Verify handler is registered
	retrievedHandler, exists := registry.GetHandler(model.TaskIDDailyCheckin)
	assert.True(t, exists)
	assert.Equal(t, handler.GetIdentifier(), retrievedHandler.GetIdentifier())
}

func TestTaskRegistry_ProcessTask_WithIdentifier(t *testing.T) {
	mockService := &MockActivityCashbackService{}
	registry := NewTaskRegistry(mockService)

	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()

	// Setup mock expectations
	mockService.On("UpdateActivity", ctx, userID).Return(nil)
	mockService.On("CompleteProgress", ctx, userID, taskID).Return(nil)

	// Create task with identifier
	taskIdentifier := model.TaskIDDailyCheckin
	task := &model.ActivityTask{
		ID:             taskID,
		Name:           "Daily Check-in",
		TaskIdentifier: &taskIdentifier,
		Frequency:      model.FrequencyDaily,
	}

	// Process task
	err := registry.ProcessTask(ctx, userID, task, map[string]interface{}{})

	// Verify
	assert.NoError(t, err)
	mockService.AssertExpectations(t)
}

func TestTaskRegistry_ProcessTask_WithoutIdentifier_FallbackToName(t *testing.T) {
	mockService := &MockActivityCashbackService{}
	registry := NewTaskRegistry(mockService)

	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()

	// Setup mock expectations
	mockService.On("UpdateActivity", ctx, userID).Return(nil)
	mockService.On("CompleteProgress", ctx, userID, taskID).Return(nil)

	// Create task without identifier but with known name
	task := &model.ActivityTask{
		ID:        taskID,
		Name:      "Daily Check-in",
		Frequency: model.FrequencyDaily,
	}

	// Process task
	err := registry.ProcessTask(ctx, userID, task, map[string]interface{}{})

	// Verify
	assert.NoError(t, err)
	mockService.AssertExpectations(t)
}

func TestTaskRegistry_ProcessTask_UnknownTask(t *testing.T) {
	mockService := &MockActivityCashbackService{}
	registry := NewTaskRegistry(mockService)

	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()

	// Create task with unknown name and no identifier
	task := &model.ActivityTask{
		ID:        taskID,
		Name:      "Unknown Task",
		Frequency: model.FrequencyDaily,
	}

	// Process task
	err := registry.ProcessTask(ctx, userID, task, map[string]interface{}{})

	// Verify error
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unknown task")
}

func TestTaskRegistry_ProcessTaskByIdentifier(t *testing.T) {
	mockService := &MockActivityCashbackService{}
	registry := NewTaskRegistry(mockService)

	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()

	// Setup mock expectations
	taskIdentifier := model.TaskIDDailyCheckin
	mockService.On("GetTasksByCategory", ctx, "daily").Return([]model.ActivityTask{
		{
			ID:             taskID,
			Name:           "Daily Check-in",
			TaskIdentifier: &taskIdentifier,
			Frequency:      model.FrequencyDaily,
		},
	}, nil)
	mockService.On("UpdateActivity", ctx, userID).Return(nil)
	mockService.On("CompleteProgress", ctx, userID, taskID).Return(nil)

	// Process task by identifier
	err := registry.ProcessTaskByIdentifier(ctx, userID, model.TaskIDDailyCheckin, "daily", map[string]interface{}{})

	// Verify
	assert.NoError(t, err)
	mockService.AssertExpectations(t)
}

func TestMemeTradeHandler_Handle(t *testing.T) {
	mockService := &MockActivityCashbackService{}
	handler := NewMemeTradeHandler(mockService)

	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()

	// Setup mock expectations
	mockService.On("GetTaskProgress", ctx, userID, taskID).Return(&model.UserTaskProgress{
		UserID: userID,
		TaskID: taskID,
	}, nil)
	mockService.On("UpdateActivity", ctx, userID).Return(nil)
	mockService.On("CompleteProgress", ctx, userID, taskID).Return(nil)

	// Create task
	task := &model.ActivityTask{
		ID:        taskID,
		Frequency: model.FrequencyDaily,
	}

	// Create trade data
	data := map[string]interface{}{
		"volume":     100.0,
		"trade_type": "MEME",
	}

	// Handle task
	err := handler.Handle(ctx, userID, task, data)

	// Verify
	assert.NoError(t, err)
	assert.Equal(t, model.TaskIDMemeTradeDaily, handler.GetIdentifier())
	assert.Equal(t, "daily", handler.GetCategory())
	mockService.AssertExpectations(t)
}

func TestMemeTradeHandler_Handle_InvalidData(t *testing.T) {
	mockService := &MockActivityCashbackService{}
	handler := NewMemeTradeHandler(mockService)

	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()

	// Setup mock expectations
	mockService.On("GetTaskProgress", ctx, userID, taskID).Return(&model.UserTaskProgress{
		UserID: userID,
		TaskID: taskID,
	}, nil)

	// Create task
	task := &model.ActivityTask{
		ID:        taskID,
		Frequency: model.FrequencyDaily,
	}

	// Create invalid trade data
	data := map[string]interface{}{
		"volume":     "invalid",
		"trade_type": "MEME",
	}

	// Handle task
	err := handler.Handle(ctx, userID, task, data)

	// Verify error
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid MEME trade volume")
}

func TestTaskIdentifierMapping(t *testing.T) {
	// Test identifier to display name mapping
	displayName := model.GetTaskDisplayName(model.TaskIDDailyCheckin)
	assert.Equal(t, "Daily Check-in", displayName)

	// Test identifier validation
	assert.True(t, model.IsValidTaskIdentifier(model.TaskIDDailyCheckin))
	assert.False(t, model.IsValidTaskIdentifier("INVALID_IDENTIFIER"))
}
