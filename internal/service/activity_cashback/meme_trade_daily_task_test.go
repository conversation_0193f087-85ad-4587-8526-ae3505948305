package activity_cashback

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MemeTradeTaskTestSuite tests the complete flow of meme trade daily task
type MemeTradeTaskTestSuite struct {
	suite.Suite
	ctx             context.Context
	mockService     *MockActivityCashbackService
	taskManager     *TaskManager
	testUserID      uuid.UUID
	testTaskID      uuid.UUID
	memeTradeTask   *model.ActivityTask
	initialProgress *model.UserTaskProgress
	helper          *test.TestHelper
}

// SetupSuite initializes the test suite
func (suite *MemeTradeTaskTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.testUserID = uuid.New()
	suite.testTaskID = uuid.New()
	suite.helper = test.NewTestHelper(suite.T())

	// Setup logger to avoid nil pointer dereference
	logger, _ := zap.NewDevelopment()
	global.GVA_LOG = logger

	// Setup mock service
	suite.mockService = &MockActivityCashbackService{}
	suite.taskManager = NewTaskManager(suite.mockService)

	// Create test meme trade task
	suite.memeTradeTask = &model.ActivityTask{
		ID:             suite.testTaskID,
		CategoryID:     1,
		Name:           "Complete one meme trade",
		Description:    &[]string{"Complete one meme trade to earn points"}[0],
		Frequency:      model.FrequencyDaily,
		TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIDMemeTradeDaily; return &id }(),
		Points:         200,
		MaxCompletions: &[]int{1}[0],
		IsActive:       true,
		StartDate:      &[]time.Time{time.Now().AddDate(0, 0, -1)}[0],
		EndDate:        nil,
	}

	// Create initial progress (not started)
	suite.initialProgress = &model.UserTaskProgress{
		ID:              uuid.New(),
		UserID:          suite.testUserID,
		TaskID:          suite.testTaskID,
		Status:          model.TaskStatusNotStarted,
		ProgressValue:   0,
		CompletionCount: 0,
		PointsEarned:    0,
		LastCompletedAt: nil,
		StreakCount:     0,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
}

// TestMemeTradeTaskFlow_FirstTimeCompletion tests the complete flow for first-time task completion
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_FirstTimeCompletion() {
	// Arrange: Setup mock expectations for first-time completion
	suite.mockService.On("GetTaskProgress", suite.ctx, suite.testUserID, suite.testTaskID).
		Return(nil, gorm.ErrRecordNotFound).Once()

	suite.mockService.On("InitializeTaskProgress", suite.ctx, suite.testUserID, suite.testTaskID).
		Return(suite.initialProgress, nil).Once()

	suite.mockService.On("UpdateActivity", suite.ctx, suite.testUserID).
		Return(nil).Once()

	suite.mockService.On("CompleteTaskWithPoints", suite.ctx, suite.testUserID, suite.testTaskID, mock.MatchedBy(func(data map[string]interface{}) bool {
		volume, hasVolume := data["volume"].(float64)
		tradeType, hasTradeType := data["trade_type"].(string)
		method, hasMethod := data["method"].(string)
		processor, hasProcessor := data["processor"].(string)

		return hasVolume && volume == 150.0 &&
			hasTradeType && tradeType == "MEME" &&
			hasMethod && method == "automated_nats_event" &&
			hasProcessor && processor == "DailyTaskProcessor"
	})).Return(nil).Once()

	// Act: Process meme trade with realistic data structure
	// This matches the actual data structure from AffiliateService.processMemeTradeTaskCompletion()
	tradeData := map[string]interface{}{
		"trade_type": "MEME",                                 // Hardcoded in affiliate service
		"volume":     150.0,                                  // From affiliateTx.VolumeUSD.InexactFloat64()
		"order_id":   "282143ee-146a-4065-9aa8-b057e4a09486", // From affiliateTx.OrderID.String()
		"user_id":    suite.testUserID.String(),              // From affiliateTx.UserID.String()
	}

	err := suite.taskManager.ProcessTask(suite.ctx, suite.testUserID, suite.memeTradeTask, tradeData)

	// Assert: Verify successful completion
	suite.NoError(err)
	suite.mockService.AssertExpectations(suite.T())
}

// TestMemeTradeTaskFlow_AlreadyCompletedToday tests when task is already completed today
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_AlreadyCompletedToday() {
	// Arrange: Setup progress that was completed today
	today := time.Now()
	completedProgress := &model.UserTaskProgress{
		ID:              uuid.New(),
		UserID:          suite.testUserID,
		TaskID:          suite.testTaskID,
		Status:          model.TaskStatusCompleted,
		ProgressValue:   1,
		CompletionCount: 1,
		PointsEarned:    200,
		LastCompletedAt: &today,
		StreakCount:     1,
		CreatedAt:       time.Now().AddDate(0, 0, -1),
		UpdatedAt:       today,
	}

	suite.mockService.On("GetTaskProgress", suite.ctx, suite.testUserID, suite.testTaskID).
		Return(completedProgress, nil).Once()

	// Act: Try to process another meme trade today with realistic data
	tradeData := map[string]interface{}{
		"trade_type": "MEME",
		"volume":     100.0,
		"order_id":   "f2c3bef2-f873-48f2-9bfd-db7c32edf043", // From NATS event example
		"user_id":    suite.testUserID.String(),
	}

	err := suite.taskManager.ProcessTask(suite.ctx, suite.testUserID, suite.memeTradeTask, tradeData)

	// Assert: Should complete without error (already completed today)
	suite.NoError(err)
	suite.mockService.AssertExpectations(suite.T())
}

// TestMemeTradeTaskFlow_InvalidTradeData tests with invalid trade data
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_InvalidTradeData() {
	// Test cases for invalid data
	testCases := []struct {
		name      string
		tradeData map[string]interface{}
		errorMsg  string
	}{
		{
			name: "Invalid volume - zero",
			tradeData: map[string]interface{}{
				"volume":     0.0,
				"trade_type": "MEME",
			},
			errorMsg: "invalid MEME trade volume",
		},
		{
			name: "Invalid volume - negative",
			tradeData: map[string]interface{}{
				"volume":     -50.0,
				"trade_type": "MEME",
			},
			errorMsg: "invalid MEME trade volume",
		},
		{
			name: "Missing volume",
			tradeData: map[string]interface{}{
				"trade_type": "MEME",
			},
			errorMsg: "invalid MEME trade volume",
		},
		{
			name: "Wrong trade type",
			tradeData: map[string]interface{}{
				"volume":     100.0,
				"trade_type": "PERPETUAL",
			},
			errorMsg: "invalid trade type for MEME task",
		},
		{
			name: "Missing trade type",
			tradeData: map[string]interface{}{
				"volume": 100.0,
			},
			errorMsg: "invalid trade type for MEME task",
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			// Arrange: Setup mock to return progress (to get past the progress check)
			suite.mockService.On("GetTaskProgress", suite.ctx, suite.testUserID, suite.testTaskID).
				Return(suite.initialProgress, nil).Once()

			// Act: Process invalid trade data
			err := suite.taskManager.ProcessTask(suite.ctx, suite.testUserID, suite.memeTradeTask, tc.tradeData)

			// Assert: Should return appropriate error
			suite.Error(err)
			suite.Contains(err.Error(), tc.errorMsg)
		})
	}
}

// TestMemeTradeTaskFlow_CompletedYesterday tests when task was completed yesterday (should allow new completion)
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_CompletedYesterday() {
	// Arrange: Setup progress that was completed yesterday
	yesterday := time.Now().AddDate(0, 0, -1)
	yesterdayProgress := &model.UserTaskProgress{
		ID:              uuid.New(),
		UserID:          suite.testUserID,
		TaskID:          suite.testTaskID,
		Status:          model.TaskStatusCompleted,
		ProgressValue:   1,
		CompletionCount: 1,
		PointsEarned:    200,
		LastCompletedAt: &yesterday,
		StreakCount:     1,
		CreatedAt:       time.Now().AddDate(0, 0, -2),
		UpdatedAt:       yesterday,
	}

	suite.mockService.On("GetTaskProgress", suite.ctx, suite.testUserID, suite.testTaskID).
		Return(yesterdayProgress, nil).Once()

	suite.mockService.On("UpdateActivity", suite.ctx, suite.testUserID).
		Return(nil).Once()

	suite.mockService.On("CompleteTaskWithPoints", suite.ctx, suite.testUserID, suite.testTaskID, mock.AnythingOfType("map[string]interface {}")).
		Return(nil).Once()

	// Act: Process meme trade today with realistic data
	tradeData := map[string]interface{}{
		"trade_type": "MEME",
		"volume":     200.0,
		"order_id":   "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
		"user_id":    suite.testUserID.String(),
	}

	err := suite.taskManager.ProcessTask(suite.ctx, suite.testUserID, suite.memeTradeTask, tradeData)

	// Assert: Should complete successfully
	suite.NoError(err)
	suite.mockService.AssertExpectations(suite.T())
}

// TestMemeTradeTaskFlow_ProgressInitializationError tests error during progress initialization
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_ProgressInitializationError() {
	// Arrange: Setup mock to fail on initialization
	suite.mockService.On("GetTaskProgress", suite.ctx, suite.testUserID, suite.testTaskID).
		Return(nil, gorm.ErrRecordNotFound).Once()

	suite.mockService.On("InitializeTaskProgress", suite.ctx, suite.testUserID, suite.testTaskID).
		Return((*model.UserTaskProgress)(nil), assert.AnError).Once()

	// Act: Process meme trade
	tradeData := map[string]interface{}{
		"volume":     100.0,
		"trade_type": "MEME",
	}

	err := suite.taskManager.ProcessTask(suite.ctx, suite.testUserID, suite.memeTradeTask, tradeData)

	// Assert: Should return initialization error
	suite.Error(err)
	suite.Contains(err.Error(), "failed to initialize task progress")
	suite.mockService.AssertExpectations(suite.T())
}

// TestMemeTradeTaskFlow_CompleteTaskWithPointsError tests error during CompleteTaskWithPoints
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_CompleteTaskWithPointsError() {
	// Arrange: Setup mock to fail on CompleteTaskWithPoints
	suite.mockService.On("GetTaskProgress", suite.ctx, suite.testUserID, suite.testTaskID).
		Return(suite.initialProgress, nil).Once()

	suite.mockService.On("UpdateActivity", suite.ctx, suite.testUserID).
		Return(nil).Once()

	suite.mockService.On("CompleteTaskWithPoints", suite.ctx, suite.testUserID, suite.testTaskID, mock.AnythingOfType("map[string]interface {}")).
		Return(assert.AnError).Once()

	// Act: Process meme trade
	tradeData := map[string]interface{}{
		"volume":     100.0,
		"trade_type": "MEME",
	}

	err := suite.taskManager.ProcessTask(suite.ctx, suite.testUserID, suite.memeTradeTask, tradeData)

	// Assert: Should return CompleteTaskWithPoints error
	suite.Error(err)
	suite.Contains(err.Error(), "failed to complete MEME trade task with points")
	suite.mockService.AssertExpectations(suite.T())
}

// TestMemeTradeTaskFlow_UpdateActivityError tests error during UpdateActivity
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_UpdateActivityError() {
	// Arrange: Setup mock to fail on UpdateActivity (but continue processing)
	suite.mockService.On("GetTaskProgress", suite.ctx, suite.testUserID, suite.testTaskID).
		Return(suite.initialProgress, nil).Once()

	suite.mockService.On("UpdateActivity", suite.ctx, suite.testUserID).
		Return(assert.AnError).Once() // This should not stop the process

	suite.mockService.On("CompleteTaskWithPoints", suite.ctx, suite.testUserID, suite.testTaskID, mock.AnythingOfType("map[string]interface {}")).
		Return(nil).Once()

	// Act: Process meme trade
	tradeData := map[string]interface{}{
		"volume":     100.0,
		"trade_type": "MEME",
	}

	err := suite.taskManager.ProcessTask(suite.ctx, suite.testUserID, suite.memeTradeTask, tradeData)

	// Assert: Should complete successfully despite UpdateActivity error
	suite.NoError(err)
	suite.mockService.AssertExpectations(suite.T())
}

// TestMemeTradeTaskFlow_GetTaskProgressError tests error during GetTaskProgress
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_GetTaskProgressError() {
	// Arrange: Setup mock to fail on GetTaskProgress (not ErrRecordNotFound)
	suite.mockService.On("GetTaskProgress", suite.ctx, suite.testUserID, suite.testTaskID).
		Return((*model.UserTaskProgress)(nil), assert.AnError).Once()

	// Act: Process meme trade
	tradeData := map[string]interface{}{
		"volume":     100.0,
		"trade_type": "MEME",
	}

	err := suite.taskManager.ProcessTask(suite.ctx, suite.testUserID, suite.memeTradeTask, tradeData)

	// Assert: Should return GetTaskProgress error
	suite.Error(err)
	suite.Contains(err.Error(), "failed to get task progress")
	suite.mockService.AssertExpectations(suite.T())
}

// TestMemeTradeTaskFlow_EdgeCaseVolumes tests edge case volumes
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_EdgeCaseVolumes() {
	testCases := []struct {
		name   string
		volume float64
	}{
		{"Very small volume", 0.01},
		{"Large volume", 999999.99},
		{"Exact 1.0", 1.0},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			// Arrange: Setup fresh mock for each test case
			mockService := &MockActivityCashbackService{}
			taskManager := NewTaskManager(mockService)

			mockService.On("GetTaskProgress", suite.ctx, suite.testUserID, suite.testTaskID).
				Return(suite.initialProgress, nil).Once()

			mockService.On("UpdateActivity", suite.ctx, suite.testUserID).
				Return(nil).Once()

			mockService.On("CompleteTaskWithPoints", suite.ctx, suite.testUserID, suite.testTaskID, mock.MatchedBy(func(data map[string]interface{}) bool {
				volume, hasVolume := data["volume"].(float64)
				return hasVolume && volume == tc.volume
			})).Return(nil).Once()

			// Act: Process meme trade with edge case volume
			tradeData := map[string]interface{}{
				"volume":     tc.volume,
				"trade_type": "MEME",
			}

			err := taskManager.ProcessTask(suite.ctx, suite.testUserID, suite.memeTradeTask, tradeData)

			// Assert: Should complete successfully
			suite.NoError(err)
			mockService.AssertExpectations(suite.T())
		})
	}
}

// TestMemeTradeTaskFlow_FullIntegration tests the complete integration flow
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_FullIntegration() {
	// This test simulates the complete flow from trade event to points awarded

	// Arrange: Setup a complete scenario
	userID := uuid.New()
	taskID := uuid.New()

	// Create a fresh mock service for this integration test
	mockService := &MockActivityCashbackService{}
	taskManager := NewTaskManager(mockService)

	// Create task with proper configuration
	task := &model.ActivityTask{
		ID:             taskID,
		CategoryID:     1,
		Name:           "Complete one meme trade",
		Description:    &[]string{"Complete one meme trade to earn points"}[0],
		Frequency:      model.FrequencyDaily,
		TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIDMemeTradeDaily; return &id }(),
		Points:         200,
		MaxCompletions: &[]int{1}[0],
		IsActive:       true,
	}

	// Setup initial progress (first time user)
	initialProgress := &model.UserTaskProgress{
		ID:              uuid.New(),
		UserID:          userID,
		TaskID:          taskID,
		Status:          model.TaskStatusNotStarted,
		ProgressValue:   0,
		CompletionCount: 0,
		PointsEarned:    0,
		LastCompletedAt: nil,
		StreakCount:     0,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// Setup mock expectations for complete flow
	mockService.On("GetTaskProgress", suite.ctx, userID, taskID).
		Return(nil, gorm.ErrRecordNotFound).Once()

	mockService.On("InitializeTaskProgress", suite.ctx, userID, taskID).
		Return(initialProgress, nil).Once()

	mockService.On("UpdateActivity", suite.ctx, userID).
		Return(nil).Once()

	// Verify that CompleteTaskWithPoints is called with correct data
	mockService.On("CompleteTaskWithPoints", suite.ctx, userID, taskID, mock.MatchedBy(func(data map[string]interface{}) bool {
		volume, hasVolume := data["volume"].(float64)
		tradeType, hasTradeType := data["trade_type"].(string)
		method, hasMethod := data["method"].(string)
		processor, hasProcessor := data["processor"].(string)

		return hasVolume && volume == 500.0 &&
			hasTradeType && tradeType == "MEME" &&
			hasMethod && method == "automated_nats_event" &&
			hasProcessor && processor == "DailyTaskProcessor"
	})).Return(nil).Once()

	// Act: Simulate realistic NATS event processing
	// This matches the actual NATS event structure and processing flow
	tradeEventData := map[string]interface{}{
		"trade_type": "MEME",                                 // Hardcoded in AffiliateService
		"volume":     500.0,                                  // From volume_usd calculation
		"order_id":   "f2c3bef2-f873-48f2-9bfd-db7c32edf043", // From NATS event
		"user_id":    userID.String(),                        // From NATS event user_id
	}

	// Process the trade event
	err := taskManager.ProcessTask(suite.ctx, userID, task, tradeEventData)

	// Assert: Verify successful completion
	suite.NoError(err, "Trade processing should complete without error")
	mockService.AssertExpectations(suite.T())

	// Additional assertions to verify the flow
	suite.T().Log("✅ MEME trade task integration test completed successfully")
	suite.T().Log("✅ User progress initialized for first-time user")
	suite.T().Log("✅ User activity updated")
	suite.T().Log("✅ Task completed with 200 points awarded")
	suite.T().Log("✅ Verification data properly structured")
}

// TestMemeTradeTaskFlow_MultipleTradesInDay tests multiple trades in same day
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_MultipleTradesInDay() {
	// This test verifies that only the first trade of the day counts for daily task

	userID := uuid.New()
	taskID := uuid.New()

	mockService := &MockActivityCashbackService{}
	taskManager := NewTaskManager(mockService)

	task := &model.ActivityTask{
		ID:             taskID,
		CategoryID:     1,
		Name:           "Complete one meme trade",
		Frequency:      model.FrequencyDaily,
		TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIDMemeTradeDaily; return &id }(),
		Points:         200,
		MaxCompletions: &[]int{1}[0],
		IsActive:       true,
	}

	// First trade - should complete the task
	suite.Run("First trade of the day", func() {
		initialProgress := &model.UserTaskProgress{
			ID:              uuid.New(),
			UserID:          userID,
			TaskID:          taskID,
			Status:          model.TaskStatusNotStarted,
			ProgressValue:   0,
			CompletionCount: 0,
			PointsEarned:    0,
			LastCompletedAt: nil,
			StreakCount:     0,
		}

		mockService.On("GetTaskProgress", suite.ctx, userID, taskID).
			Return(initialProgress, nil).Once()

		mockService.On("UpdateActivity", suite.ctx, userID).
			Return(nil).Once()

		mockService.On("CompleteTaskWithPoints", suite.ctx, userID, taskID, mock.AnythingOfType("map[string]interface {}")).
			Return(nil).Once()

		tradeData := map[string]interface{}{
			"volume":     100.0,
			"trade_type": "MEME",
		}

		err := taskManager.ProcessTask(suite.ctx, userID, task, tradeData)
		suite.NoError(err)
	})

	// Second trade - should be ignored (already completed today)
	suite.Run("Second trade of the day", func() {
		now := time.Now()
		completedProgress := &model.UserTaskProgress{
			ID:              uuid.New(),
			UserID:          userID,
			TaskID:          taskID,
			Status:          model.TaskStatusCompleted,
			ProgressValue:   1,
			CompletionCount: 1,
			PointsEarned:    200,
			LastCompletedAt: &now,
			StreakCount:     1,
		}

		mockService.On("GetTaskProgress", suite.ctx, userID, taskID).
			Return(completedProgress, nil).Once()

		// No other mock calls expected since task is already completed

		tradeData := map[string]interface{}{
			"volume":     200.0,
			"trade_type": "MEME",
		}

		err := taskManager.ProcessTask(suite.ctx, userID, task, tradeData)
		suite.NoError(err) // Should complete without error but do nothing
	})

	mockService.AssertExpectations(suite.T())
}

// TestMemeTradeTaskFlow_RealNATSEventData tests with actual NATS event data structure
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_RealNATSEventData() {
	// This test uses the exact data structure from your NATS event example

	userID := uuid.MustParse("0198c1b6-f7c9-79f3-a0b7-52bccf786ef7") // From NATS event
	taskID := uuid.New()

	mockService := &MockActivityCashbackService{}
	taskManager := NewTaskManager(mockService)

	task := &model.ActivityTask{
		ID:             taskID,
		CategoryID:     1,
		Name:           "Complete one meme trade",
		Frequency:      model.FrequencyDaily,
		TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIDMemeTradeDaily; return &id }(),
		Points:         200,
		MaxCompletions: &[]int{1}[0],
		IsActive:       true,
	}

	// Setup initial progress (first time user)
	initialProgress := &model.UserTaskProgress{
		ID:              uuid.New(),
		UserID:          userID,
		TaskID:          taskID,
		Status:          model.TaskStatusNotStarted,
		ProgressValue:   0,
		CompletionCount: 0,
		PointsEarned:    0,
		LastCompletedAt: nil,
		StreakCount:     0,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// Setup mock expectations
	mockService.On("GetTaskProgress", suite.ctx, userID, taskID).
		Return(nil, gorm.ErrRecordNotFound).Once()

	mockService.On("InitializeTaskProgress", suite.ctx, userID, taskID).
		Return(initialProgress, nil).Once()

	mockService.On("UpdateActivity", suite.ctx, userID).
		Return(nil).Once()

	// Verify that CompleteTaskWithPoints is called with correct data
	mockService.On("CompleteTaskWithPoints", suite.ctx, userID, taskID, mock.MatchedBy(func(data map[string]interface{}) bool {
		volume, hasVolume := data["volume"].(float64)
		tradeType, hasTradeType := data["trade_type"].(string)
		method, hasMethod := data["method"].(string)
		processor, hasProcessor := data["processor"].(string)

		// Volume should be calculated from quote_amount (0.000032864 SOL)
		// In reality, this would be converted to USD using SOL price
		return hasVolume && volume == 0.000032864 &&
			hasTradeType && tradeType == "MEME" &&
			hasMethod && method == "automated_nats_event" &&
			hasProcessor && processor == "DailyTaskProcessor"
	})).Return(nil).Once()

	// Act: Process with actual NATS event data structure
	// This simulates the data that would be created by AffiliateService.processMemeTradeTaskCompletion()
	// based on the NATS event you provided
	tradeData := map[string]interface{}{
		"trade_type": "MEME",                                 // Hardcoded in affiliate service
		"volume":     0.000032864,                            // From quote_amount in NATS event
		"order_id":   "f2c3bef2-f873-48f2-9bfd-db7c32edf043", // From NATS event
		"user_id":    userID.String(),                        // From NATS event
	}

	err := taskManager.ProcessTask(suite.ctx, userID, task, tradeData)

	// Assert: Verify successful completion
	suite.NoError(err, "Real NATS event data processing should complete without error")
	mockService.AssertExpectations(suite.T())

	// Additional verification
	suite.T().Log("✅ Real NATS event data processed successfully")
	suite.T().Log("✅ User ID from NATS event:", userID.String())
	suite.T().Log("✅ Order ID from NATS event: f2c3bef2-f873-48f2-9bfd-db7c32edf043")
	suite.T().Log("✅ Volume from quote_amount: 0.000032864")
	suite.T().Log("✅ Trade type hardcoded as MEME")
}

// TestMemeTradeTaskFlow_VolumeCalculationRealistic tests with realistic volume calculations
func (suite *MemeTradeTaskTestSuite) TestMemeTradeTaskFlow_VolumeCalculationRealistic() {
	// Test with volumes that would actually trigger task completion
	// Based on your affiliate_transactions data, volume_usd is typically small for meme trades

	testCases := []struct {
		name        string
		volumeUSD   float64
		shouldPass  bool
		description string
	}{
		{
			name:        "Very small volume from NATS",
			volumeUSD:   0.002974233, // From your affiliate_transactions example
			shouldPass:  true,
			description: "Small volume should still complete daily task",
		},
		{
			name:        "Zero volume",
			volumeUSD:   0.0,
			shouldPass:  false,
			description: "Zero volume should fail validation",
		},
		{
			name:        "Typical meme trade volume",
			volumeUSD:   1.50,
			shouldPass:  true,
			description: "Typical small meme trade volume",
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			mockService := &MockActivityCashbackService{}
			taskManager := NewTaskManager(mockService)

			userID := uuid.New()
			taskID := uuid.New()

			task := &model.ActivityTask{
				ID:             taskID,
				Name:           "Complete one meme trade",
				Frequency:      model.FrequencyDaily,
				TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIDMemeTradeDaily; return &id }(),
				Points:         200,
				IsActive:       true,
			}

			if tc.shouldPass {
				// Setup mocks for successful completion
				initialProgress := &model.UserTaskProgress{
					ID:              uuid.New(),
					UserID:          userID,
					TaskID:          taskID,
					Status:          model.TaskStatusNotStarted,
					ProgressValue:   0,
					CompletionCount: 0,
					PointsEarned:    0,
					LastCompletedAt: nil,
				}

				mockService.On("GetTaskProgress", suite.ctx, userID, taskID).
					Return(initialProgress, nil).Once()

				mockService.On("UpdateActivity", suite.ctx, userID).
					Return(nil).Once()

				mockService.On("CompleteTaskWithPoints", suite.ctx, userID, taskID, mock.AnythingOfType("map[string]interface {}")).
					Return(nil).Once()
			} else {
				// For invalid volume, we expect it to fail before calling other services
				mockService.On("GetTaskProgress", suite.ctx, userID, taskID).
					Return(&model.UserTaskProgress{}, nil).Once()
			}

			// Act
			tradeData := map[string]interface{}{
				"trade_type": "MEME",
				"volume":     tc.volumeUSD,
				"order_id":   uuid.New().String(),
				"user_id":    userID.String(),
			}

			err := taskManager.ProcessTask(suite.ctx, userID, task, tradeData)

			// Assert
			if tc.shouldPass {
				suite.NoError(err, tc.description)
			} else {
				suite.Error(err, tc.description)
			}

			mockService.AssertExpectations(suite.T())
		})
	}
}

// Run the test suite
func TestMemeTradeTaskSuite(t *testing.T) {
	suite.Run(t, new(MemeTradeTaskTestSuite))
}
