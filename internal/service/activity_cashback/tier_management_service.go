package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TierManagementService implements TierManagementServiceInterface
type TierManagementService struct {
	tierInfoRepo    activity_cashback.UserTierInfoRepositoryInterface
	tierBenefitRepo activity_cashback.TierBenefitRepositoryInterface
}

// NewTierManagementService creates a new TierManagementService
func NewTierManagementService(
	tierInfoRepo activity_cashback.UserTierInfoRepositoryInterface,
	tierBenefitRepo activity_cashback.TierBenefitRepositoryInterface,
) TierManagementServiceInterface {
	return &TierManagementService{
		tierInfoRepo:    tierInfoRepo,
		tierBenefitRepo: tierBenefitRepo,
	}
}

// GetUserTierInfo retrieves user tier information with optimized monthly reset check
func (s *TierManagementService) GetUserTierInfo(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	tierInfo, err := s.tierInfoRepo.GetByUserID(ctx, userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create default tier info if not exists
			return s.CreateUserTierInfo(ctx, userID)
		}
		return nil, fmt.Errorf("failed to get user tier info: %w", err)
	}

	// Optimized: Only check and perform reset if it's actually needed
	// This reduces unnecessary logging and processing
	if tierInfo.IsResetNeededForCurrentMonth() {
		global.GVA_LOG.Info("Monthly reset needed for user (lazy reset)",
			zap.String("user_id", userID.String()),
			zap.Int("current_active_days", tierInfo.ActiveDaysThisMonth),
			zap.Time("last_reset_at", func() time.Time {
				if tierInfo.MonthlyResetAt != nil {
					return *tierInfo.MonthlyResetAt
				}
				return time.Time{}
			}()))

		tierInfo.ResetMonthlyStats()
		if err := s.UpdateUserTierInfo(ctx, tierInfo); err != nil {
			global.GVA_LOG.Error("Failed to reset monthly stats for user",
				zap.Error(err),
				zap.String("user_id", userID.String()))
			// Don't return error here, just log it and continue with reset data
		}

		global.GVA_LOG.Info("Monthly stats reset completed for user (lazy reset)",
			zap.String("user_id", userID.String()),
			zap.Int("reset_active_days", tierInfo.ActiveDaysThisMonth))
	}

	return tierInfo, nil
}

// GetUserTierInfoWithoutReset retrieves user tier information without performing monthly reset check
// Use this method when you only need to read the data without potential modifications
func (s *TierManagementService) GetUserTierInfoWithoutReset(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	tierInfo, err := s.tierInfoRepo.GetByUserID(ctx, userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create default tier info if not exists
			return s.CreateUserTierInfo(ctx, userID)
		}
		return nil, fmt.Errorf("failed to get user tier info: %w", err)
	}

	return tierInfo, nil
}

// CreateUserTierInfo creates default tier information for a user
func (s *TierManagementService) CreateUserTierInfo(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	tierInfo := &model.UserTierInfo{
		UserID:      userID,
		CurrentTier: 1, // Start at tier 1
		TotalPoints: 0,
	}

	if err := s.tierInfoRepo.Create(ctx, tierInfo); err != nil {
		return nil, fmt.Errorf("failed to create user tier info: %w", err)
	}

	global.GVA_LOG.Info("User tier info created", zap.String("user_id", userID.String()))
	return tierInfo, nil
}

// UpdateUserTierInfo updates user tier information
func (s *TierManagementService) UpdateUserTierInfo(ctx context.Context, tierInfo *model.UserTierInfo) error {
	global.GVA_LOG.Info("Attempting to update user tier info",
		zap.String("user_id", tierInfo.UserID.String()),
		zap.Int("total_points", tierInfo.TotalPoints),
		zap.Int("current_tier", tierInfo.CurrentTier))

	if err := s.tierInfoRepo.Update(ctx, tierInfo); err != nil {
		global.GVA_LOG.Error("Failed to update user tier info in repository",
			zap.Error(err),
			zap.String("user_id", tierInfo.UserID.String()),
			zap.Int("total_points", tierInfo.TotalPoints))
		return fmt.Errorf("failed to update user tier info: %w", err)
	}

	global.GVA_LOG.Info("Successfully updated user tier info",
		zap.String("user_id", tierInfo.UserID.String()),
		zap.Int("active_days_this_month", tierInfo.ActiveDaysThisMonth),
		zap.Int("total_points", tierInfo.TotalPoints))

	return nil
}

// AddPoints adds points to a user's total
func (s *TierManagementService) AddPoints(ctx context.Context, userID uuid.UUID, points int, source string) error {
	global.GVA_LOG.Info("Starting AddPoints operation",
		zap.String("user_id", userID.String()),
		zap.Int("points", points),
		zap.String("source", source))

	// Get fresh tier info from database to avoid stale data
	// This ensures we get the latest state including any recent activity updates
	tierInfo, err := s.tierInfoRepo.GetByUserID(ctx, userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create default tier info if not exists
			tierInfo, err = s.CreateUserTierInfo(ctx, userID)
			if err != nil {
				return fmt.Errorf("failed to create user tier info: %w", err)
			}
		} else {
			global.GVA_LOG.Error("Failed to get user tier info in AddPoints",
				zap.Error(err),
				zap.String("user_id", userID.String()))
			return fmt.Errorf("failed to get user tier info: %w", err)
		}
	}

	global.GVA_LOG.Info("Retrieved user tier info for AddPoints",
		zap.String("user_id", userID.String()),
		zap.Int("current_points", tierInfo.TotalPoints),
		zap.Int("current_active_days", tierInfo.ActiveDaysThisMonth),
		zap.Int("points_to_add", points))

	oldPoints := tierInfo.TotalPoints
	oldTier := tierInfo.CurrentTier
	tierInfo.AddPoints(points)

	global.GVA_LOG.Info("Points added to tier info object",
		zap.String("user_id", userID.String()),
		zap.Int("old_points", oldPoints),
		zap.Int("new_points", tierInfo.TotalPoints),
		zap.Int("active_days_preserved", tierInfo.ActiveDaysThisMonth))

	if err := s.UpdateUserTierInfo(ctx, tierInfo); err != nil {
		global.GVA_LOG.Error("Failed to update user tier info in AddPoints",
			zap.Error(err),
			zap.String("user_id", userID.String()),
			zap.Int("points", points))
		return fmt.Errorf("failed to update user tier info: %w", err)
	}

	global.GVA_LOG.Info("Points added to user successfully",
		zap.String("user_id", userID.String()),
		zap.Int("points", points),
		zap.String("source", source),
		zap.Int("total_points", tierInfo.TotalPoints))

	// Check for tier upgrade after adding points
	newTier, err := s.CheckTierUpgrade(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to check tier upgrade after adding points",
			zap.Error(err),
			zap.String("user_id", userID.String()),
			zap.Int("points", points))
		// Don't return error here as points were already added successfully
	} else if newTier != nil {
		global.GVA_LOG.Info("User tier upgraded after adding points",
			zap.String("user_id", userID.String()),
			zap.Int("old_tier", oldTier),
			zap.Int("new_tier", newTier.TierLevel),
			zap.Int("points_added", points),
			zap.String("source", source))
	}

	return nil
}

// GetUserPoints retrieves user's total points
func (s *TierManagementService) GetUserPoints(ctx context.Context, userID uuid.UUID) (int, error) {
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return 0, err
	}
	return tierInfo.TotalPoints, nil
}

// GetUserRank retrieves user's rank based on points
func (s *TierManagementService) GetUserRank(ctx context.Context, userID uuid.UUID) (int, error) {
	return s.tierInfoRepo.GetUserRankByPoints(ctx, userID)
}

// CheckTierUpgrade checks if user is eligible for tier upgrade
func (s *TierManagementService) CheckTierUpgrade(ctx context.Context, userID uuid.UUID) (*model.TierBenefit, error) {
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get next tier
	nextTier, err := s.tierBenefitRepo.GetNextTier(ctx, tierInfo.CurrentTier)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// User is already at max tier
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get next tier: %w", err)
	}

	// Check if user has enough points
	if tierInfo.CanUpgradeTier(nextTier.MinPoints) {
		// Upgrade the user
		if err := s.UpgradeUserTier(ctx, userID, nextTier.TierLevel); err != nil {
			return nil, fmt.Errorf("failed to upgrade user tier: %w", err)
		}
		return nextTier, nil
	}

	return nil, nil
}

// UpgradeUserTier upgrades user to a new tier
func (s *TierManagementService) UpgradeUserTier(ctx context.Context, userID uuid.UUID, newTier int) error {
	// Get fresh tier info from database
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return err
	}

	oldTier := tierInfo.CurrentTier

	// Only upgrade if the new tier is actually higher
	if newTier <= oldTier {
		global.GVA_LOG.Debug("Skipping tier upgrade - new tier is not higher than current",
			zap.String("user_id", userID.String()),
			zap.Int("current_tier", oldTier),
			zap.Int("new_tier", newTier))
		return nil
	}

	tierInfo.UpgradeTier(newTier)

	if err := s.UpdateUserTierInfo(ctx, tierInfo); err != nil {
		return err
	}

	global.GVA_LOG.Info("User tier upgraded",
		zap.String("user_id", userID.String()),
		zap.Int("old_tier", oldTier),
		zap.Int("new_tier", newTier))

	return nil
}

// GetNextTierRequirement gets the next tier and points needed
func (s *TierManagementService) GetNextTierRequirement(ctx context.Context, userID uuid.UUID) (*model.TierBenefit, int, error) {
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return nil, 0, err
	}

	nextTier, err := s.tierBenefitRepo.GetNextTier(ctx, tierInfo.CurrentTier)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, 0, nil // Already at max tier
		}
		return nil, 0, fmt.Errorf("failed to get next tier: %w", err)
	}

	pointsNeeded := tierInfo.GetPointsToNextTier(nextTier.MinPoints)
	return nextTier, pointsNeeded, nil
}

// TriggerTierUpgradeCheck manually triggers a tier upgrade check for a specific user
// This is useful for immediate tier upgrades after point additions
func (s *TierManagementService) TriggerTierUpgradeCheck(ctx context.Context, userID uuid.UUID) (*model.TierBenefit, error) {
	global.GVA_LOG.Info("Manually triggering tier upgrade check",
		zap.String("user_id", userID.String()))

	return s.CheckTierUpgrade(ctx, userID)
}

// AddCashback adds cashback to user's claimable amount
func (s *TierManagementService) AddCashback(ctx context.Context, userID uuid.UUID, amount decimal.Decimal) error {
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return err
	}

	tierInfo.AddCashback(amount)

	if err := s.UpdateUserTierInfo(ctx, tierInfo); err != nil {
		return err
	}

	global.GVA_LOG.Info("Cashback added to user",
		zap.String("user_id", userID.String()),
		zap.String("amount", amount.String()))

	return nil
}

// AddTradingVolume adds trading volume to user's accumulated total
func (s *TierManagementService) AddTradingVolume(ctx context.Context, userID uuid.UUID, volume decimal.Decimal) error {
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return err
	}

	tierInfo.AddTradingVolume(volume)

	if err := s.UpdateUserTierInfo(ctx, tierInfo); err != nil {
		return err
	}

	global.GVA_LOG.Info("Trading volume added to user",
		zap.String("user_id", userID.String()),
		zap.String("volume", volume.String()),
		zap.String("total_volume", tierInfo.TradingVolumeUSD.String()))

	return nil
}

// GetClaimableCashback retrieves user's claimable cashback amount
func (s *TierManagementService) GetClaimableCashback(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return decimal.Zero, err
	}
	return tierInfo.ClaimableCashbackUSD, nil
}

// ClaimCashback processes cashback claim
func (s *TierManagementService) ClaimCashback(ctx context.Context, userID uuid.UUID, amount decimal.Decimal) error {
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return err
	}

	if err := tierInfo.ClaimCashback(amount); err != nil {
		return fmt.Errorf("failed to claim cashback: %w", err)
	}

	if err := s.UpdateUserTierInfo(ctx, tierInfo); err != nil {
		return err
	}

	global.GVA_LOG.Info("Cashback claimed by user",
		zap.String("user_id", userID.String()),
		zap.String("amount", amount.String()))

	return nil
}

// UpdateActivity updates user's activity for the day with database-level locking to prevent race conditions
func (s *TierManagementService) UpdateActivity(ctx context.Context, userID uuid.UUID) error {
	// Use database transaction with row-level locking to prevent race conditions
	return s.tierInfoRepo.UpdateActivityWithLock(ctx, userID, func(tierInfo *model.UserTierInfo) error {
		// Optimized: Skip expensive logging if reset was done recently
		shouldLogDetails := !tierInfo.IsResetRecentlyDone()

		if shouldLogDetails {
			global.GVA_LOG.Info("Updating user activity",
				zap.String("user_id", userID.String()),
				zap.Int("current_active_days", tierInfo.ActiveDaysThisMonth),
				zap.Time("last_activity", func() time.Time {
					if tierInfo.LastActivityDate != nil {
						return *tierInfo.LastActivityDate
					}
					return time.Time{}
				}()))
		}

		// Check if monthly reset is needed within the locked transaction
		// Use improved method to prevent double reset
		if tierInfo.IsResetNeededForCurrentMonth() {
			global.GVA_LOG.Info("Monthly reset needed for user within activity update (lazy reset)",
				zap.String("user_id", userID.String()),
				zap.Int("current_active_days", tierInfo.ActiveDaysThisMonth),
				zap.Time("monthly_reset_at", func() time.Time {
					if tierInfo.MonthlyResetAt != nil {
						return *tierInfo.MonthlyResetAt
					}
					return time.Time{}
				}()))

			tierInfo.ResetMonthlyStats()

			global.GVA_LOG.Info("Monthly reset completed - stats reset to 0",
				zap.String("user_id", userID.String()),
				zap.Int("active_days_after_reset", tierInfo.ActiveDaysThisMonth))
		}

		// Update activity AFTER any monthly reset has been applied
		global.GVA_LOG.Info("About to call UpdateActivity",
			zap.String("user_id", userID.String()),
			zap.Int("active_days_before_update", tierInfo.ActiveDaysThisMonth),
			zap.Time("last_activity_date", func() time.Time {
				if tierInfo.LastActivityDate != nil {
					return *tierInfo.LastActivityDate
				}
				return time.Time{}
			}()))

		tierInfo.UpdateActivity()

		global.GVA_LOG.Info("UpdateActivity completed",
			zap.String("user_id", userID.String()),
			zap.Int("active_days_after_update", tierInfo.ActiveDaysThisMonth),
			zap.Time("last_activity_date_after", func() time.Time {
				if tierInfo.LastActivityDate != nil {
					return *tierInfo.LastActivityDate
				}
				return time.Time{}
			}()))

		// Optimized: Only log detailed info when necessary
		if shouldLogDetails {
			global.GVA_LOG.Info("Activity updated",
				zap.String("user_id", userID.String()),
				zap.Int("new_active_days", tierInfo.ActiveDaysThisMonth),
				zap.Time("new_last_activity", func() time.Time {
					if tierInfo.LastActivityDate != nil {
						return *tierInfo.LastActivityDate
					}
					return time.Time{}
				}()))
		}

		return nil
	})
}

// ResetMonthlyStats resets monthly statistics for all users
func (s *TierManagementService) ResetMonthlyStats(ctx context.Context) error {
	users, err := s.tierInfoRepo.GetUsersNeedingMonthlyReset(ctx)
	if err != nil {
		return fmt.Errorf("failed to get users needing monthly reset: %w", err)
	}

	var userIDs []uuid.UUID
	for _, user := range users {
		userIDs = append(userIDs, user.UserID)
	}

	if len(userIDs) > 0 {
		if err := s.tierInfoRepo.BulkUpdateMonthlyStats(ctx, userIDs); err != nil {
			return fmt.Errorf("failed to bulk update monthly stats: %w", err)
		}
	}

	global.GVA_LOG.Info("Monthly stats reset completed", zap.Int("users_reset", len(userIDs)))
	return nil
}

// GetMonthlyStats retrieves monthly statistics for a user
func (s *TierManagementService) GetMonthlyStats(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	tierInfo, err := s.GetUserTierInfo(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Check if monthly reset is needed
	if tierInfo.ShouldResetMonthly() {
		tierInfo.ResetMonthlyStats()
		if err := s.UpdateUserTierInfo(ctx, tierInfo); err != nil {
			global.GVA_LOG.Error("Failed to reset monthly stats for user", zap.Error(err), zap.String("user_id", userID.String()))
		}
	}

	return tierInfo, nil
}
