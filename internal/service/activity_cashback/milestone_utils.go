package activity_cashback

import (
	"fmt"
	"sort"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// MilestoneDisplayInfo contains information about how to display a milestone
type MilestoneDisplayInfo struct {
	CurrentMilestone *model.ConsecutiveCheckinMilestone
	NextMilestone    *model.ConsecutiveCheckinMilestone
	DisplayName      string
	DisplayNameLang  map[string]string // Multilingual display names
	Progress         int               // Current consecutive days
	IsCompleted      bool
}

// GetNextMilestone returns the next milestone the user should work towards
func GetNextMilestone(milestones []model.ConsecutiveCheckinMilestone, currentStreak int) *model.ConsecutiveCheckinMilestone {
	if len(milestones) == 0 {
		return nil
	}

	// Sort milestones by days ascending
	sortedMilestones := make([]model.ConsecutiveCheckinMilestone, len(milestones))
	copy(sortedMilestones, milestones)
	sort.Slice(sortedMilestones, func(i, j int) bool {
		return sortedMilestones[i].Days < sortedMilestones[j].Days
	})

	// Find the next milestone that hasn't been reached
	for _, milestone := range sortedMilestones {
		if currentStreak < milestone.Days {
			return &milestone
		}
	}

	// If all milestones are completed, return the highest one
	return &sortedMilestones[len(sortedMilestones)-1]
}

// GetCurrentMilestone returns the milestone that was just completed or is in progress
func GetCurrentMilestone(milestones []model.ConsecutiveCheckinMilestone, currentStreak int) *model.ConsecutiveCheckinMilestone {
	if len(milestones) == 0 {
		return nil
	}

	// Sort milestones by days descending to find the highest completed milestone
	sortedMilestones := make([]model.ConsecutiveCheckinMilestone, len(milestones))
	copy(sortedMilestones, milestones)
	sort.Slice(sortedMilestones, func(i, j int) bool {
		return sortedMilestones[i].Days > sortedMilestones[j].Days
	})

	// Find the highest milestone that has been reached
	for _, milestone := range sortedMilestones {
		if currentStreak >= milestone.Days {
			return &milestone
		}
	}

	return nil
}

// GetMilestoneDisplayInfo returns comprehensive display information for a milestone
func GetMilestoneDisplayInfo(milestones []model.ConsecutiveCheckinMilestone, currentStreak int, preferredLang string) *MilestoneDisplayInfo {
	nextMilestone := GetNextMilestone(milestones, currentStreak)
	currentMilestone := GetCurrentMilestone(milestones, currentStreak)

	info := &MilestoneDisplayInfo{
		CurrentMilestone: currentMilestone,
		NextMilestone:    nextMilestone,
		Progress:         currentStreak,
		IsCompleted:      false,
	}

	// Determine which milestone to display and completion status
	displayMilestone := nextMilestone
	if displayMilestone == nil {
		// No next milestone means all milestones are completed
		displayMilestone = currentMilestone
		info.IsCompleted = true
	} else if currentMilestone != nil && nextMilestone != nil && currentMilestone.Days == nextMilestone.Days {
		// Current milestone is the same as next milestone (highest milestone reached)
		info.IsCompleted = true
	}

	if displayMilestone != nil {
		// Generate display names
		info.DisplayNameLang = make(map[string]string)

		if displayMilestone.Name != nil {
			// Use custom milestone names if available
			info.DisplayNameLang["en"] = displayMilestone.Name.En
			if displayMilestone.Name.Zh != nil {
				info.DisplayNameLang["zh"] = *displayMilestone.Name.Zh
			}
			if displayMilestone.Name.Ja != nil {
				info.DisplayNameLang["ja"] = *displayMilestone.Name.Ja
			}
			if displayMilestone.Name.Hi != nil {
				info.DisplayNameLang["hi"] = *displayMilestone.Name.Hi
			}
			if displayMilestone.Name.Hk != nil {
				info.DisplayNameLang["hk"] = *displayMilestone.Name.Hk
			}
			if displayMilestone.Name.Vi != nil {
				info.DisplayNameLang["vi"] = *displayMilestone.Name.Vi
			}
		} else {
			// Generate default names
			info.DisplayNameLang["en"] = fmt.Sprintf("%d-Day Consecutive Check-in", displayMilestone.Days)
			info.DisplayNameLang["zh"] = fmt.Sprintf("%d天连续签到", displayMilestone.Days)
			info.DisplayNameLang["ja"] = fmt.Sprintf("%d日連続チェックイン", displayMilestone.Days)
			info.DisplayNameLang["hi"] = fmt.Sprintf("%d दिन लगातार चेक-इन", displayMilestone.Days)
			info.DisplayNameLang["hk"] = fmt.Sprintf("%d天連續簽到", displayMilestone.Days)
			info.DisplayNameLang["vi"] = fmt.Sprintf("Check-in liên tiếp %d ngày", displayMilestone.Days)
		}

		// Set the display name based on preferred language
		if name, exists := info.DisplayNameLang[preferredLang]; exists && name != "" {
			info.DisplayName = name
		} else {
			info.DisplayName = info.DisplayNameLang["en"] // Fallback to English
		}
	}

	return info
}

// FormatTaskNameWithMilestone formats the task name to include milestone information
func FormatTaskNameWithMilestone(baseTaskName *model.MultilingualName, milestoneInfo *MilestoneDisplayInfo, preferredLang string) *model.MultilingualName {
	if milestoneInfo == nil || milestoneInfo.NextMilestone == nil {
		return baseTaskName
	}

	result := &model.MultilingualName{}

	// Format English name
	if milestoneInfo.IsCompleted {
		result.En = fmt.Sprintf("%s - Completed!", baseTaskName.En)
	} else {
		result.En = fmt.Sprintf("%s - %s", baseTaskName.En, milestoneInfo.DisplayNameLang["en"])
	}

	// Format other languages if available
	if baseTaskName.Zh != nil {
		zhName := milestoneInfo.DisplayNameLang["zh"]
		if milestoneInfo.IsCompleted {
			result.Zh = &[]string{fmt.Sprintf("%s - 已完成！", *baseTaskName.Zh)}[0]
		} else {
			result.Zh = &[]string{fmt.Sprintf("%s - %s", *baseTaskName.Zh, zhName)}[0]
		}
	}

	if baseTaskName.Ja != nil {
		jaName := milestoneInfo.DisplayNameLang["ja"]
		if milestoneInfo.IsCompleted {
			result.Ja = &[]string{fmt.Sprintf("%s - 完了！", *baseTaskName.Ja)}[0]
		} else {
			result.Ja = &[]string{fmt.Sprintf("%s - %s", *baseTaskName.Ja, jaName)}[0]
		}
	}

	if baseTaskName.Hi != nil {
		hiName := milestoneInfo.DisplayNameLang["hi"]
		if milestoneInfo.IsCompleted {
			result.Hi = &[]string{fmt.Sprintf("%s - पूर्ण!", *baseTaskName.Hi)}[0]
		} else {
			result.Hi = &[]string{fmt.Sprintf("%s - %s", *baseTaskName.Hi, hiName)}[0]
		}
	}

	if baseTaskName.Hk != nil {
		hkName := milestoneInfo.DisplayNameLang["hk"]
		if milestoneInfo.IsCompleted {
			result.Hk = &[]string{fmt.Sprintf("%s - 已完成！", *baseTaskName.Hk)}[0]
		} else {
			result.Hk = &[]string{fmt.Sprintf("%s - %s", *baseTaskName.Hk, hkName)}[0]
		}
	}

	if baseTaskName.Vi != nil {
		viName := milestoneInfo.DisplayNameLang["vi"]
		if milestoneInfo.IsCompleted {
			result.Vi = &[]string{fmt.Sprintf("%s - Hoàn thành!", *baseTaskName.Vi)}[0]
		} else {
			result.Vi = &[]string{fmt.Sprintf("%s - %s", *baseTaskName.Vi, viName)}[0]
		}
	}

	return result
}

// FormatTaskNameWithCurrentMilestone formats the task name to include current target milestone information
func FormatTaskNameWithCurrentMilestone(baseTaskName *model.MultilingualName, targetMilestone *model.ConsecutiveCheckinMilestone, preferredLang string) *model.MultilingualName {
	if targetMilestone == nil {
		return baseTaskName
	}

	result := &model.MultilingualName{}

	// Format English name
	result.En = fmt.Sprintf("%s - %d days", baseTaskName.En, targetMilestone.Days)

	// Format other languages if available
	if baseTaskName.Zh != nil {
		result.Zh = &[]string{fmt.Sprintf("%s - %d天", *baseTaskName.Zh, targetMilestone.Days)}[0]
	}

	if baseTaskName.Ja != nil {
		result.Ja = &[]string{fmt.Sprintf("%s - %d日", *baseTaskName.Ja, targetMilestone.Days)}[0]
	}

	if baseTaskName.Hi != nil {
		result.Hi = &[]string{fmt.Sprintf("%s - %d दिन", *baseTaskName.Hi, targetMilestone.Days)}[0]
	}

	if baseTaskName.Hk != nil {
		result.Hk = &[]string{fmt.Sprintf("%s - %d天", *baseTaskName.Hk, targetMilestone.Days)}[0]
	}

	if baseTaskName.Vi != nil {
		result.Vi = &[]string{fmt.Sprintf("%s - %d ngày", *baseTaskName.Vi, targetMilestone.Days)}[0]
	}

	return result
}

// GetMilestoneProgress returns progress information for display
func GetMilestoneProgress(milestones []model.ConsecutiveCheckinMilestone, currentStreak int) (current int, total int, percentage float64) {
	nextMilestone := GetNextMilestone(milestones, currentStreak)
	if nextMilestone == nil {
		// No milestones defined
		return currentStreak, currentStreak, 100.0
	}

	// Check if all milestones are completed
	allCompleted := true
	for _, m := range milestones {
		if currentStreak < m.Days {
			allCompleted = false
			break
		}
	}

	if allCompleted {
		// All milestones completed - find the highest milestone
		maxDays := 0
		for _, m := range milestones {
			if m.Days > maxDays {
				maxDays = m.Days
			}
		}
		// For completed milestones, show the highest milestone as both current and total
		return maxDays, maxDays, 100.0
	}

	current = currentStreak
	total = nextMilestone.Days
	if total > 0 {
		percentage = float64(current) / float64(total) * 100.0
	}

	return current, total, percentage
}
