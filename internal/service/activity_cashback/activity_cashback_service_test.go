package activity_cashback

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// Test cases
func TestActivityCashbackService_InitializeUserForActivityCashback(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()
	userID := uuid.New()

	mockService.On("InitializeUserForActivityCashback", ctx, userID).Return(nil)

	err := mockService.InitializeUserForActivityCashback(ctx, userID)

	assert.NoError(t, err)
	mockService.AssertExpectations(t)
}

func TestActivityCashbackService_CompleteTask(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()
	verificationData := map[string]interface{}{
		"volume": 100.0,
		"type":   "MEME",
	}

	mockService.On("CompleteTask", ctx, userID, taskID, verificationData).Return(nil)

	err := mockService.CompleteTask(ctx, userID, taskID, verificationData)

	assert.NoError(t, err)
	mockService.AssertExpectations(t)
}

func TestActivityCashbackService_GetUserDashboard(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()
	userID := uuid.New()

	expectedDashboard := &UserDashboard{
		UserTierInfo: &model.UserTierInfo{
			UserID:      userID,
			CurrentTier: 1,
			TotalPoints: 100,
		},
		PointsToNextTier: 400,
		UserRank:         10,
	}

	mockService.On("GetUserDashboard", ctx, userID).Return(expectedDashboard, nil)

	dashboard, err := mockService.GetUserDashboard(ctx, userID)

	assert.NoError(t, err)
	assert.Equal(t, expectedDashboard, dashboard)
	assert.Equal(t, 1, dashboard.UserTierInfo.CurrentTier)
	assert.Equal(t, 100, dashboard.UserTierInfo.TotalPoints)
	mockService.AssertExpectations(t)
}

func TestActivityCashbackService_GetTaskCenter(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()
	userID := uuid.New()

	expectedTaskCenter := &TaskCenter{
		CompletedToday:    2,
		PointsEarnedToday: 50,
		Categories: []TaskCategoryWithTasks{
			{
				Category: model.TaskCategory{
					ID:          1,
					Name:        "daily",
					DisplayName: "Daily Tasks",
				},
				Tasks: []TaskWithProgress{
					{
						Task: model.ActivityTask{
							ID:     uuid.New(),
							Name:   "Daily Check-in",
							Points: 5,
						},
					},
				},
			},
		},
	}

	mockService.On("GetTaskCenter", ctx, userID).Return(expectedTaskCenter, nil)

	taskCenter, err := mockService.GetTaskCenter(ctx, userID)

	assert.NoError(t, err)
	assert.Equal(t, expectedTaskCenter, taskCenter)
	assert.Equal(t, 2, taskCenter.CompletedToday)
	assert.Equal(t, 50, taskCenter.PointsEarnedToday)
	assert.Len(t, taskCenter.Categories, 1)
	mockService.AssertExpectations(t)
}

func TestActivityCashbackService_VerifyTradingTask(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()

	tests := []struct {
		name           string
		tradingData    map[string]interface{}
		expectedResult bool
		expectedError  error
	}{
		{
			name: "Valid MEME trade",
			tradingData: map[string]interface{}{
				"volume":     100.0,
				"trade_type": "MEME",
			},
			expectedResult: true,
			expectedError:  nil,
		},
		{
			name: "Valid Perpetual trade",
			tradingData: map[string]interface{}{
				"volume":     500.0,
				"trade_type": "PERPETUAL",
			},
			expectedResult: true,
			expectedError:  nil,
		},
		{
			name: "Invalid trade - insufficient volume",
			tradingData: map[string]interface{}{
				"volume":     0.5,
				"trade_type": "MEME",
			},
			expectedResult: false,
			expectedError:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService.On("VerifyTradingTask", ctx, userID, taskID, tt.tradingData).Return(tt.expectedResult, tt.expectedError)

			result, err := mockService.VerifyTradingTask(ctx, userID, taskID, tt.tradingData)

			assert.Equal(t, tt.expectedResult, result)
			assert.Equal(t, tt.expectedError, err)
		})
	}

	mockService.AssertExpectations(t)
}

func TestActivityCashbackService_TaskReset(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()

	tests := []struct {
		name      string
		resetFunc func() error
		mockCall  string
	}{
		{
			name: "Reset Daily Tasks",
			resetFunc: func() error {
				return mockService.ResetDailyTasks(ctx)
			},
			mockCall: "ResetDailyTasks",
		},
		{
			name: "Reset Weekly Tasks",
			resetFunc: func() error {
				return mockService.ResetWeeklyTasks(ctx)
			},
			mockCall: "ResetWeeklyTasks",
		},
		{
			name: "Reset Monthly Tasks",
			resetFunc: func() error {
				return mockService.ResetMonthlyTasks(ctx)
			},
			mockCall: "ResetMonthlyTasks",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService.On(tt.mockCall, ctx).Return(nil)

			err := tt.resetFunc()

			assert.NoError(t, err)
		})
	}

	mockService.AssertExpectations(t)
}

// Integration test helper functions
func createTestTask() *model.ActivityTask {
	return &model.ActivityTask{
		ID:         uuid.New(),
		CategoryID: 1,
		Name:       "Test Task",
		Frequency:  model.FrequencyDaily,
		Points:     10,
		IsActive:   true,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
}

func createTestUserTierInfo(userID uuid.UUID) *model.UserTierInfo {
	return &model.UserTierInfo{
		UserID:      userID,
		CurrentTier: 1,
		TotalPoints: 0,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}

func createTestUserTaskProgress(userID, taskID uuid.UUID) *model.UserTaskProgress {
	return &model.UserTaskProgress{
		ID:            uuid.New(),
		UserID:        userID,
		TaskID:        taskID,
		Status:        model.TaskStatusNotStarted,
		ProgressValue: 0,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
}

func TestActivityCashbackService_GetActivityCashbackSummary(t *testing.T) {
	mockService := new(MockActivityCashbackService)
	ctx := context.Background()
	userID := uuid.New()

	// Create expected summary data
	expectedSummary := &ActivityCashbackSummary{
		CurrentLevel:                3,
		CurrentLevelName:            "Lv3",
		NextLevel:                   &[]int{4}[0],
		NextLevelName:               &[]string{"Lv4"}[0],
		CurrentScore:                2250,
		TotalScoreForNextLevel:      &[]int{4000}[0],
		ScoreRequiredToUpgrade:      &[]int{1750}[0],
		ProgressPercentage:          64.3,
		AccumulatedTradingVolumeUSD: decimal.NewFromFloat(969858.12),
		ActiveLogonDays:             15,
		AccumulatedCashbackUSD:      decimal.NewFromFloat(18858.12),
		ClaimableCashbackUSD:        decimal.NewFromFloat(150.50),
		ClaimedCashbackUSD:          decimal.NewFromFloat(18707.62),
		CurrentTierColor:            &[]string{"#8B5CF6"}[0],
		CurrentTierIcon:             &[]string{"🔥"}[0],
		NextTierColor:               &[]string{"#F59E0B"}[0],
		NextTierIcon:                &[]string{"⭐"}[0],
	}

	mockService.On("GetActivityCashbackSummary", ctx, userID).Return(expectedSummary, nil)

	result, err := mockService.GetActivityCashbackSummary(ctx, userID)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedSummary.CurrentLevel, result.CurrentLevel)
	assert.Equal(t, expectedSummary.CurrentLevelName, result.CurrentLevelName)
	assert.Equal(t, expectedSummary.NextLevel, result.NextLevel)
	assert.Equal(t, expectedSummary.NextLevelName, result.NextLevelName)
	assert.Equal(t, expectedSummary.CurrentScore, result.CurrentScore)
	assert.Equal(t, expectedSummary.TotalScoreForNextLevel, result.TotalScoreForNextLevel)
	assert.Equal(t, expectedSummary.ScoreRequiredToUpgrade, result.ScoreRequiredToUpgrade)
	assert.Equal(t, expectedSummary.ProgressPercentage, result.ProgressPercentage)
	assert.True(t, expectedSummary.AccumulatedTradingVolumeUSD.Equal(result.AccumulatedTradingVolumeUSD))
	assert.Equal(t, expectedSummary.ActiveLogonDays, result.ActiveLogonDays)
	assert.True(t, expectedSummary.AccumulatedCashbackUSD.Equal(result.AccumulatedCashbackUSD))
	assert.True(t, expectedSummary.ClaimableCashbackUSD.Equal(result.ClaimableCashbackUSD))
	assert.True(t, expectedSummary.ClaimedCashbackUSD.Equal(result.ClaimedCashbackUSD))
	assert.Equal(t, expectedSummary.CurrentTierColor, result.CurrentTierColor)
	assert.Equal(t, expectedSummary.CurrentTierIcon, result.CurrentTierIcon)
	assert.Equal(t, expectedSummary.NextTierColor, result.NextTierColor)
	assert.Equal(t, expectedSummary.NextTierIcon, result.NextTierIcon)

	mockService.AssertExpectations(t)
}
