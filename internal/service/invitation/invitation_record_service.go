package invitation

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
)

type InvitationRecordService struct {
	userRepo        repo.InvitationRepo
	affiliateRepo   transaction.AffiliateTransactionRepositoryInterface
	hyperLiquidRepo transaction.HyperLiquidTransactionRepositoryInterface
}

func NewInvitationRecordService() *InvitationRecordService {
	return &InvitationRecordService{
		userRepo:        &agent_referral.InvitationRepository{},
		affiliateRepo:   transaction.NewAffiliateTransactionRepository(),
		hyperLiquidRepo: transaction.NewHyperLiquidTransactionRepository(),
	}
}

type InvitationRecord struct {
	Address           string  `json:"address"`
	TransactionVolume float64 `json:"transactionVolume"`
	InvitedWithdrawal float64 `json:"invitedWithdrawal"`
	Date              string  `json:"date"`
	ChainID           int     `json:"chainId"`
	Token             string  `json:"token"`
}

type InvitationRecordRequest struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

type InvitationRecordResponse struct {
	Data     []*InvitationRecord `json:"data"`
	Total    int                 `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"page_size"`
}

func (s *InvitationRecordService) GetInvitationRecords(ctx context.Context, userID uuid.UUID, request *InvitationRecordRequest) (*InvitationRecordResponse, error) {
	// 获取当前用户的邀请奖励记录，而不是所有用户
	var records []*InvitationRecord

	// meme 数据 - 获取当前用户作为邀请人的奖励记录
	memeRecords, err := s.getMemeInvitationRecords(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get MEME invitation records",
			zap.String("user_id", userID.String()),
			zap.Error(err))
		// 继续处理，不返回错误
	} else {
		records = append(records, memeRecords...)
	}

	// 合约数据 - 获取当前用户作为邀请人的奖励记录
	contractRecords, err := s.getContractInvitationRecords(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get contract invitation records",
			zap.String("user_id", userID.String()),
			zap.Error(err))
		// 继续处理，不返回错误
	} else {
		records = append(records, contractRecords...)
	}

	// 按日期排序
	s.sortRecordsByDate(records)

	// 应用分页
	total := len(records)
	start := (request.Page - 1) * request.PageSize
	end := start + request.PageSize

	if start >= total {
		start = total
	}
	if end > total {
		end = total
	}

	var paginatedRecords []*InvitationRecord
	if start < total {
		paginatedRecords = records[start:end]
	}

	return &InvitationRecordResponse{
		Data:     paginatedRecords,
		Total:    total,
		Page:     request.Page,
		PageSize: request.PageSize,
	}, nil
}

// getMemeInvitationRecords 获取 MEME 邀请记录
// address查询AffiliateTransaction表的UserAddress，关联ActivityCashback的查询CashbackAmountUSD为InvitedWithdrawal
// ActivityCashback的CreatedAt为Date, 这个地址的交易量CashbackAmountSOL*SolPriceUSD
func (s *InvitationRecordService) getMemeInvitationRecords(ctx context.Context, userID uuid.UUID) ([]*InvitationRecord, error) {
	var records []*InvitationRecord

	// 查询 MEME 交易和返现数据
	var result []struct {
		UserAddress         string          `json:"user_address"`
		CommissionAmountSol decimal.Decimal `json:"commission_amount_sol"`
		VolumeUSD           decimal.Decimal `json:"volume_usd"`
		CreatedAt           time.Time       `json:"created_at"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Table("affiliate_transactions at").
		Select(`
			at.user_address,
			mcl.commission_amount_sol as commission_amount_sol,
			at.volume_usd,
			mcl.created_at
		`).
		Joins("JOIN meme_commission_ledger mcl ON at.order_id = mcl.source_transaction_id").
		Where("mcl.recipient_user_id = ? AND at.status = ? AND mcl.source_transaction_type = ?", userID, "Completed", "Direct").
		Find(&result).Error

	if err != nil {
		return nil, err
	}

	// 直接转换查询结果，不进行分组和相加
	for _, item := range result {
		global.GVA_LOG.Info("Sol amount", zap.String("amount", item.CommissionAmountSol.String()))
		record := &InvitationRecord{
			Address:           item.UserAddress,
			TransactionVolume: item.VolumeUSD.InexactFloat64(),
			InvitedWithdrawal: item.CommissionAmountSol.InexactFloat64(),
			ChainID:           int(utils.ChainIDSolanaInt),
			Token:             utils.WSOL_ADDRESS,
			Date:              item.CreatedAt.Format("2006-01-02"),
		}
		records = append(records, record)
	}

	return records, nil
}

// getContractInvitationRecords 获取合约邀请记录
// user表通过userID 关联 address查询HyperLiquidTransaction的WalletAddress,关联CommissionLedger的查询CommissionAmount
// CommissionLedger 的CreatedAt为Date, 这个用户地址的交易量HyperLiquidTransaction的avg_price * totalSz
func (s *InvitationRecordService) getContractInvitationRecords(ctx context.Context, userID uuid.UUID) ([]*InvitationRecord, error) {
	var records []*InvitationRecord

	// 查询合约交易和返佣数据
	var result []struct {
		WalletAddress    string          `json:"wallet_address"`
		CommissionAmount decimal.Decimal `json:"commission_amount"`
		AvgPrice         decimal.Decimal `json:"avg_price"`
		TotalSz          decimal.Decimal `json:"total_sz"`
		CreatedAt        time.Time       `json:"created_at"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Table("hyper_liquid_transactions hlt").
		Select(`
			hlt.wallet_address,
			cl.commission_amount,
			hlt.avg_price,
			hlt.size as total_sz,
			cl.created_at
		`).
		Joins("JOIN commission_ledger cl ON hlt.wallet_address = (SELECT wallet_address FROM user_wallets WHERE user_id = cl.source_user_id LIMIT 1)").
		Where("cl.recipient_user_id = ? AND cl.source_transaction_type = ? AND cl.status = ?", userID, "Direct", "PENDING_CLAIM").
		Find(&result).Error

	if err != nil {
		return nil, err
	}

	// 直接转换查询结果，不进行分组和相加
	for _, item := range result {
		transactionVolume := item.AvgPrice.Mul(item.TotalSz)
		record := &InvitationRecord{
			Address:           item.WalletAddress,
			TransactionVolume: transactionVolume.InexactFloat64(),
			InvitedWithdrawal: item.CommissionAmount.InexactFloat64(),
			ChainID:           int(utils.ChainIDArbitrumOneInt),
			Token:             utils.USDC_ARB_ADDRESS,
			Date:              item.CreatedAt.Format("2006-01-02"),
		}
		records = append(records, record)
	}

	return records, nil
}

// sortRecordsByDate 按日期排序记录 DESC
func (s *InvitationRecordService) sortRecordsByDate(records []*InvitationRecord) {
	for i := 0; i < len(records)-1; i++ {
		for j := 0; j < len(records)-i-1; j++ {
			if records[j].Date < records[j+1].Date {
				records[j], records[j+1] = records[j+1], records[j]
			}
		}
	}
}
