package data_overview

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

// DataOverviewServiceInterface defines the interface for data overview operations
type DataOverviewServiceInterface interface {
	GetRebateAmountChart(ctx context.Context, userID uuid.UUID, timeRange string) (*response.RebateAmountChartResponse, error)
	GetTransactionVolumeChart(ctx context.Context, userID uuid.UUID, timeRange string) (*response.TransactionVolumeChartResponse, error)
	GetInvitationCountChart(ctx context.Context, userID uuid.UUID, timeRange string) (*response.InvitationCountChartResponse, error)
}

// DataOverviewService implements data overview operations
type DataOverviewService struct {
	affiliateRepo      transaction.AffiliateTransactionRepositoryInterface
	hyperLiquidRepo    transaction.HyperLiquidTransactionRepositoryInterface
	commissionRepo     transaction.CommissionLedgerRepositoryInterface
	memeCommissionRepo transaction.MemeCommissionLedgerRepositoryInterface
	userRepo           transaction.UserRepositoryInterface
}

// NewDataOverviewService creates a new data overview service
func NewDataOverviewService() DataOverviewServiceInterface {
	return &DataOverviewService{
		affiliateRepo:      transaction.NewAffiliateTransactionRepository(),
		hyperLiquidRepo:    transaction.NewHyperLiquidTransactionRepository(),
		commissionRepo:     transaction.NewCommissionLedgerRepository(),
		memeCommissionRepo: transaction.NewMemeCommissionLedgerRepository(),
		userRepo:           transaction.NewUserRepository(),
	}
}

// getMemeRebateAmountByUserIDAndPeriod gets MEME rebate amount from MemeCommissionLedger table for a user within a time period
func (s *DataOverviewService) getMemeRebateAmountByUserIDAndPeriod(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error) {
	rebate, err := s.memeCommissionRepo.GetRebateAmountByUserIDAndTypeAndPeriod(ctx, userID, "MEME", startTime, endTime)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get meme rebate amount: %w", err)
	}

	return rebate, nil
}

// GetRebateAmountChart retrieves rebate amount chart data based on the specified time range
func (s *DataOverviewService) GetRebateAmountChart(ctx context.Context, userID uuid.UUID, timeRange string) (*response.RebateAmountChartResponse, error) {
	// Get all downline users (up to 3 levels)
	allDownlineUsers, err := s.userRepo.GetAllDownlineUsers(ctx, userID, 3)
	if err != nil {
		global.GVA_LOG.Error("Failed to get downline users", zap.Error(err))
		return nil, fmt.Errorf("failed to get downline users: %w", err)
	}

	// Get chart data based on time range
	var chartData []*response.RebateAmountChartDataPoint
	var startTime, endTime time.Time

	switch timeRange {
	case "TODAY":
		endTime = time.Now()
		startTime = endTime.Add(-24 * time.Hour).UTC()
		chartData, err = s.getHourlyRebateChartData(ctx, userID, allDownlineUsers, startTime, endTime)
	case "LAST_30_DAYS":
		endTime = time.Now()
		startTime = endTime.Add(-30 * 24 * time.Hour).UTC()
		chartData, err = s.getDailyRebateChartData(ctx, userID, allDownlineUsers, startTime, endTime)
	case "LAST_60_DAYS":
		endTime = time.Now()
		startTime = endTime.Add(-60 * 24 * time.Hour).UTC()
		chartData, err = s.getDailyRebateChartData(ctx, userID, allDownlineUsers, startTime, endTime)
	case "ALL_TIME":
		endTime = time.Now()
		startTime = endTime.Add(-12 * 30 * 24 * time.Hour).UTC() // 12 months
		chartData, err = s.getMonthlyRebateChartData(ctx, userID, allDownlineUsers, startTime, endTime)
	default:
		return nil, fmt.Errorf("unsupported time range: %s", timeRange)
	}

	if err != nil {
		return nil, err
	}

	// Get current values (latest data point)
	currentValues := s.getCurrentRebateValues(chartData)

	return &response.RebateAmountChartResponse{
		CurrentValues: currentValues,
		Data:          chartData,
		Success:       true,
	}, nil
}

// getHourlyRebateChartData gets rebate amount data for the past 24 hours with hourly granularity
func (s *DataOverviewService) getHourlyRebateChartData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) ([]*response.RebateAmountChartDataPoint, error) {
	var chartData []*response.RebateAmountChartDataPoint

	for i := 0; i < 24; i++ {
		periodStart := startTime.Add(time.Duration(i) * time.Hour)
		periodEnd := periodStart.Add(time.Hour)

		rebateAmount, err := s.getPeriodRebateData(ctx, userID, downlineUsers, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		period := periodStart.Format("15:04")

		chartData = append(chartData, &response.RebateAmountChartDataPoint{
			Timestamp: periodStart,
			Period:    period,
			Contract:  rebateAmount.Contract,
			Meme:      rebateAmount.Meme,
			All:       rebateAmount.All,
		})
	}

	return chartData, nil
}

// getDailyRebateChartData gets rebate amount data for the specified days with daily granularity
func (s *DataOverviewService) getDailyRebateChartData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) ([]*response.RebateAmountChartDataPoint, error) {
	var chartData []*response.RebateAmountChartDataPoint

	days := int(endTime.Sub(startTime).Hours() / 24)
	for i := 0; i < days; i++ {
		periodStart := startTime.AddDate(0, 0, i)
		periodEnd := periodStart.AddDate(0, 0, 1)

		rebateAmount, err := s.getPeriodRebateData(ctx, userID, downlineUsers, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		period := periodStart.Format("01/02")

		chartData = append(chartData, &response.RebateAmountChartDataPoint{
			Timestamp: periodStart,
			Period:    period,
			Contract:  rebateAmount.Contract,
			Meme:      rebateAmount.Meme,
			All:       rebateAmount.All,
		})
	}

	return chartData, nil
}

// getMonthlyRebateChartData gets rebate amount data for the past 12 months with monthly granularity
func (s *DataOverviewService) getMonthlyRebateChartData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) ([]*response.RebateAmountChartDataPoint, error) {
	var chartData []*response.RebateAmountChartDataPoint

	for i := 0; i < 12; i++ {
		periodStart := startTime.AddDate(0, i, 0)
		periodEnd := periodStart.AddDate(0, 1, 0)

		rebateAmount, err := s.getPeriodRebateData(ctx, userID, downlineUsers, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		period := periodStart.Format("2006-01")

		chartData = append(chartData, &response.RebateAmountChartDataPoint{
			Timestamp: periodStart,
			Period:    period,
			Contract:  rebateAmount.Contract,
			Meme:      rebateAmount.Meme,
			All:       rebateAmount.All,
		})
	}

	return chartData, nil
}

// getPeriodRebateData gets rebate amount data for a specific time period
func (s *DataOverviewService) getPeriodRebateData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) (*response.DataOverviewCategory, error) {
	// Get MEME rebate amount
	memeRebate, err := s.getMemeRebateAmountByUserIDAndPeriod(ctx, userID, startTime, endTime)
	if err != nil {
		return nil, err
	}

	// Get CONTRACT rebate amount
	contractRebate, err := s.commissionRepo.GetRebateAmountByUserIDAndTypeAndPeriod(ctx, userID, "CONTRACT", startTime, endTime)
	if err != nil {
		return nil, err
	}

	// Calculate total (USDT equivalent)
	totalRebate := memeRebate.Add(contractRebate)

	rebateAmount := &response.DataOverviewCategory{
		All:      totalRebate,
		Meme:     memeRebate,
		Contract: contractRebate,
	}

	return rebateAmount, nil
}

// getCurrentRebateValues gets current values by accumulating all data points
func (s *DataOverviewService) getCurrentRebateValues(chartData []*response.RebateAmountChartDataPoint) *response.RebateAmountCurrentValues {
	currentValues := &response.RebateAmountCurrentValues{
		All:      decimal.Zero,
		Meme:     decimal.Zero,
		Contract: decimal.Zero,
	}

	// Calculate total by accumulating all data points
	totalAll := decimal.Zero
	totalMeme := decimal.Zero
	totalContract := decimal.Zero

	// Accumulate all data points
	for _, point := range chartData {
		totalAll = totalAll.Add(point.All)
		totalMeme = totalMeme.Add(point.Meme)
		totalContract = totalContract.Add(point.Contract)
	}

	currentValues.All = totalAll
	currentValues.Meme = totalMeme
	currentValues.Contract = totalContract

	return currentValues
}

// GetTransactionVolumeChart retrieves transaction volume chart data based on the specified time range
func (s *DataOverviewService) GetTransactionVolumeChart(ctx context.Context, userID uuid.UUID, timeRange string) (*response.TransactionVolumeChartResponse, error) {
	// Get all downline users (up to 3 levels)
	allDownlineUsers, err := s.userRepo.GetAllDownlineUsers(ctx, userID, 3)
	if err != nil {
		global.GVA_LOG.Error("Failed to get downline users", zap.Error(err))
		return nil, fmt.Errorf("failed to get downline users: %w", err)
	}

	// Get chart data based on time range
	var chartData []*response.TransactionVolumeChartDataPoint
	var startTime, endTime time.Time

	switch timeRange {
	case "TODAY":
		endTime = time.Now()
		startTime = endTime.Add(-24 * time.Hour).UTC()
		chartData, err = s.getHourlyTransactionVolumeChartData(ctx, userID, allDownlineUsers, startTime, endTime)
	case "LAST_30_DAYS":
		endTime = time.Now()
		startTime = endTime.Add(-30 * 24 * time.Hour).UTC()
		chartData, err = s.getDailyTransactionVolumeChartData(ctx, userID, allDownlineUsers, startTime, endTime)
	case "LAST_60_DAYS":
		endTime = time.Now()
		startTime = endTime.Add(-60 * 24 * time.Hour).UTC()
		chartData, err = s.getDailyTransactionVolumeChartData(ctx, userID, allDownlineUsers, startTime, endTime)
	case "ALL_TIME":
		endTime = time.Now()
		startTime = endTime.Add(-12 * 30 * 24 * time.Hour).UTC() // 12 months
		chartData, err = s.getMonthlyTransactionVolumeChartData(ctx, userID, allDownlineUsers, startTime, endTime)
	default:
		return nil, fmt.Errorf("unsupported time range: %s", timeRange)
	}

	if err != nil {
		return nil, err
	}

	// Get current values (latest data point)
	currentValues := s.getCurrentTransactionVolumeValues(chartData)

	return &response.TransactionVolumeChartResponse{
		CurrentValues: currentValues,
		Data:          chartData,
		Success:       true,
	}, nil
}

// getHourlyTransactionVolumeChartData gets transaction volume data for the past 24 hours with hourly granularity
func (s *DataOverviewService) getHourlyTransactionVolumeChartData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) ([]*response.TransactionVolumeChartDataPoint, error) {
	var chartData []*response.TransactionVolumeChartDataPoint

	for i := 0; i < 24; i++ {
		periodStart := startTime.Add(time.Duration(i) * time.Hour)
		periodEnd := periodStart.Add(time.Hour)

		volumeData, err := s.getPeriodTransactionVolumeData(ctx, userID, downlineUsers, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		period := periodStart.Format("15:04")

		chartData = append(chartData, &response.TransactionVolumeChartDataPoint{
			Timestamp: periodStart,
			Period:    period,
			Contract:  volumeData.Contract,
			Meme:      volumeData.Meme,
			All:       volumeData.All,
		})
	}

	return chartData, nil
}

// getDailyTransactionVolumeChartData gets transaction volume data for the specified days with daily granularity
func (s *DataOverviewService) getDailyTransactionVolumeChartData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) ([]*response.TransactionVolumeChartDataPoint, error) {
	var chartData []*response.TransactionVolumeChartDataPoint

	days := int(endTime.Sub(startTime).Hours() / 24)
	for i := 0; i < days; i++ {
		periodStart := startTime.AddDate(0, 0, i)
		periodEnd := periodStart.AddDate(0, 0, 1)

		volumeData, err := s.getPeriodTransactionVolumeData(ctx, userID, downlineUsers, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		period := periodStart.Format("01/02")

		chartData = append(chartData, &response.TransactionVolumeChartDataPoint{
			Timestamp: periodStart,
			Period:    period,
			Contract:  volumeData.Contract,
			Meme:      volumeData.Meme,
			All:       volumeData.All,
		})
	}

	return chartData, nil
}

// getMonthlyTransactionVolumeChartData gets transaction volume data for the past 12 months with monthly granularity
func (s *DataOverviewService) getMonthlyTransactionVolumeChartData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) ([]*response.TransactionVolumeChartDataPoint, error) {
	var chartData []*response.TransactionVolumeChartDataPoint

	for i := 0; i < 12; i++ {
		periodStart := startTime.AddDate(0, i, 0)
		periodEnd := periodStart.AddDate(0, 1, 0)

		volumeData, err := s.getPeriodTransactionVolumeData(ctx, userID, downlineUsers, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		period := periodStart.Format("2006-01")

		chartData = append(chartData, &response.TransactionVolumeChartDataPoint{
			Timestamp: periodStart,
			Period:    period,
			Contract:  volumeData.Contract,
			Meme:      volumeData.Meme,
			All:       volumeData.All,
		})
	}

	return chartData, nil
}

// getPeriodTransactionVolumeData gets transaction volume data for a specific time period
func (s *DataOverviewService) getPeriodTransactionVolumeData(ctx context.Context, userID uuid.UUID, downlineUsers []uuid.UUID, startTime, endTime time.Time) (*response.DataOverviewCategory, error) {
	// Include user's own ID in the list of users to query
	allUsers := append([]uuid.UUID{userID}, downlineUsers...)

	// Get MEME transaction volume
	memeVolume, err := s.getMemeTransactionVolumeByUserIDAndPeriod(ctx, allUsers, startTime, endTime)
	if err != nil {
		return nil, err
	}

	// Get CONTRACT transaction volume
	contractVolume, err := s.getContractTransactionVolumeByUserIDAndPeriod(ctx, allUsers, startTime, endTime)
	if err != nil {
		return nil, err
	}

	// Calculate total (USDT equivalent)
	totalVolume := memeVolume.Add(contractVolume)

	volumeData := &response.DataOverviewCategory{
		All:      totalVolume,
		Meme:     memeVolume,
		Contract: contractVolume,
	}

	return volumeData, nil
}

// getMemeTransactionVolumeByUserIDAndPeriod gets MEME transaction volume from AffiliateTransaction table for users within a time period
func (s *DataOverviewService) getMemeTransactionVolumeByUserIDAndPeriod(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error) {
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	// Query to calculate total MEME transaction volume
	// Join AffiliateTransaction with SolPriceSnapshot to get the price at transaction time
	query := `
		SELECT COALESCE(SUM(
			at.quote_amount * COALESCE(sps.price, 0)
		), 0) as total_amount
		FROM affiliate_transactions at
		LEFT JOIN LATERAL (
			SELECT price 
			FROM sol_price_snapshots 
			WHERE timestamp <= at.created_at 
			ORDER BY timestamp DESC 
			LIMIT 1
		) sps ON true
		WHERE at.user_id IN ?
		AND at.created_at::timestamp >= ?::timestamp 
		AND at.created_at::timestamp < ?::timestamp
		AND at.status = 'Completed'
	`

	err := global.GVA_DB.WithContext(ctx).Debug().
		Raw(query, userIDs, startTimeUTC, endTimeUTC).
		Scan(&result).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get total MEME volume from AffiliateTransaction, returning 0", zap.Error(err))
		return decimal.Zero, nil
	}

	return result.TotalAmount, nil
}

// getContractTransactionVolumeByUserIDAndPeriod gets CONTRACT transaction volume from HyperLiquidTransaction table for users within a time period
func (s *DataOverviewService) getContractTransactionVolumeByUserIDAndPeriod(ctx context.Context, userIDs []uuid.UUID, startTime, endTime time.Time) (decimal.Decimal, error) {
	if len(userIDs) == 0 {
		return decimal.Zero, nil
	}

	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	// Query to calculate total contract transaction volume
	// Sum of AvgPrice * TotalSz for all HyperLiquid transactions
	query := `
		SELECT COALESCE(SUM(
			COALESCE(hlt.avg_price, 0) * COALESCE(CAST(hlt.total_sz AS DECIMAL), 0)
		), 0) as total_amount
		FROM hyper_liquid_transactions hlt
		WHERE hlt.user_id IN ?
		AND hlt.created_time >= ? 
		AND hlt.created_time < ?
		AND hlt.status = 'filled'
		AND hlt.avg_price IS NOT NULL
		AND hlt.total_sz IS NOT NULL
	`

	err := global.GVA_DB.WithContext(ctx).Debug().
		Raw(query, userIDs, startTimeUTC, endTimeUTC).
		Scan(&result).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get total contract volume from HyperLiquidTransaction, returning 0", zap.Error(err))
		return decimal.Zero, nil
	}

	return result.TotalAmount, nil
}

// getCurrentTransactionVolumeValues gets current values by accumulating all data points
func (s *DataOverviewService) getCurrentTransactionVolumeValues(chartData []*response.TransactionVolumeChartDataPoint) *response.TransactionVolumeCurrentValues {
	currentValues := &response.TransactionVolumeCurrentValues{
		All:      decimal.Zero,
		Meme:     decimal.Zero,
		Contract: decimal.Zero,
	}

	// Calculate total by accumulating all data points
	totalAll := decimal.Zero
	totalMeme := decimal.Zero
	totalContract := decimal.Zero

	// Accumulate all data points
	for _, point := range chartData {
		totalAll = totalAll.Add(point.All)
		totalMeme = totalMeme.Add(point.Meme)
		totalContract = totalContract.Add(point.Contract)
	}

	currentValues.All = totalAll
	currentValues.Meme = totalMeme
	currentValues.Contract = totalContract

	return currentValues
}

// GetInvitationCountChart retrieves invitation count chart data based on the specified time range
func (s *DataOverviewService) GetInvitationCountChart(ctx context.Context, userID uuid.UUID, timeRange string) (*response.InvitationCountChartResponse, error) {
	// Get chart data based on time range
	var allData []*response.InvitationCountChartDataPoint
	var startTime, endTime time.Time
	var err error

	switch timeRange {
	case "TODAY":
		endTime = time.Now()
		startTime = endTime.Add(-24 * time.Hour).UTC()
		allData, err = s.getHourlyInvitationCountChartData(ctx, userID, startTime, endTime)
	case "LAST_30_DAYS":
		endTime = time.Now()
		startTime = endTime.Add(-30 * 24 * time.Hour).UTC()
		allData, err = s.getDailyInvitationCountChartData(ctx, userID, startTime, endTime)
	case "LAST_60_DAYS":
		endTime = time.Now()
		startTime = endTime.Add(-60 * 24 * time.Hour).UTC()
		allData, err = s.getDailyInvitationCountChartData(ctx, userID, startTime, endTime)
	case "ALL_TIME":
		endTime = time.Now()
		startTime = endTime.Add(-12 * 30 * 24 * time.Hour).UTC() // 12 months
		allData, err = s.getMonthlyInvitationCountChartData(ctx, userID, startTime, endTime)
	default:
		return nil, fmt.Errorf("unsupported time range: %s", timeRange)
	}

	if err != nil {
		return nil, err
	}

	// Get current values (total invitation count)
	currentValues := s.getCurrentInvitationCountValues(allData)

	return &response.InvitationCountChartResponse{
		CurrentValues: currentValues,
		All:           allData,
		Success:       true,
	}, nil
}

// getHourlyInvitationCountChartData gets invitation count data for the past 24 hours with hourly granularity
func (s *DataOverviewService) getHourlyInvitationCountChartData(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) ([]*response.InvitationCountChartDataPoint, error) {
	var allData []*response.InvitationCountChartDataPoint

	for i := 0; i < 24; i++ {
		periodStart := startTime.Add(time.Duration(i) * time.Hour)
		periodEnd := periodStart.Add(time.Hour)

		count, err := s.userRepo.GetInvitationCountByUserIDAndPeriod(ctx, userID, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		period := periodStart.Format("15:04")

		allData = append(allData, &response.InvitationCountChartDataPoint{
			Timestamp: periodStart,
			Period:    period,
			Value:     count,
		})
	}

	return allData, nil
}

// getDailyInvitationCountChartData gets invitation count data for the specified days with daily granularity
func (s *DataOverviewService) getDailyInvitationCountChartData(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) ([]*response.InvitationCountChartDataPoint, error) {
	var allData []*response.InvitationCountChartDataPoint

	days := int(endTime.Sub(startTime).Hours() / 24)
	for i := 0; i < days; i++ {
		periodStart := startTime.AddDate(0, 0, i)
		periodEnd := periodStart.AddDate(0, 0, 1)

		count, err := s.userRepo.GetInvitationCountByUserIDAndPeriod(ctx, userID, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		period := periodStart.Format("01/02")

		allData = append(allData, &response.InvitationCountChartDataPoint{
			Timestamp: periodStart,
			Period:    period,
			Value:     count,
		})
	}

	return allData, nil
}

// getMonthlyInvitationCountChartData gets invitation count data for the past 12 months with monthly granularity
func (s *DataOverviewService) getMonthlyInvitationCountChartData(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) ([]*response.InvitationCountChartDataPoint, error) {
	var allData []*response.InvitationCountChartDataPoint

	for i := 0; i < 12; i++ {
		periodStart := startTime.AddDate(0, i, 0)
		periodEnd := periodStart.AddDate(0, 1, 0)

		count, err := s.userRepo.GetInvitationCountByUserIDAndPeriod(ctx, userID, periodStart, periodEnd)
		if err != nil {
			return nil, err
		}

		period := periodStart.Format("2006-01")

		allData = append(allData, &response.InvitationCountChartDataPoint{
			Timestamp: periodStart,
			Period:    period,
			Value:     count,
		})
	}

	return allData, nil
}

// getCurrentInvitationCountValues gets current values by accumulating all data points
func (s *DataOverviewService) getCurrentInvitationCountValues(allData []*response.InvitationCountChartDataPoint) *response.InvitationCountCurrentValues {
	currentValues := &response.InvitationCountCurrentValues{
		All: 0,
	}

	// Calculate total by accumulating all data points
	totalAll := 0

	// Accumulate all data points for All
	for _, point := range allData {
		totalAll += point.Value
	}

	currentValues.All = totalAll

	return currentValues
}
