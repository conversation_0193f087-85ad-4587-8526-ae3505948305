package affiliate

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
)

type MemeCommissionService struct {
	affiliateRepo        repo.AffiliateRepositoryInterface
	userRepo             transaction.UserRepositoryInterface
	levelRepo            repo.LevelRepo
	activityCashbackRepo repo.ActivityCashbackRepositoryInterface
	memeCommissionRepo   transaction.MemeCommissionLedgerRepositoryInterface
}

// NewMemeCommissionService creates a new meme commission service
func NewMemeCommissionService() *MemeCommissionService {
	return &MemeCommissionService{
		affiliateRepo:        repo.NewAffiliateRepository(),
		userRepo:             transaction.NewUserRepository(),
		levelRepo:            repo.NewLevelRepository(),
		activityCashbackRepo: repo.NewActivityCashbackRepository(),
		memeCommissionRepo:   transaction.NewMemeCommissionLedgerRepository(),
	}
}

// processMemeCommission processes meme commission distribution for upline users
func (s *MemeCommissionService) ProcessMemeCommission(ctx context.Context, affiliateTx *model.AffiliateTransaction, commissionAmountUSD decimal.Decimal) error {
	// Get referral information for the user
	referralInfo, err := s.getReferralInfo(ctx, affiliateTx.UserID)
	if err != nil {
		global.GVA_LOG.Debug("No referral found for user, skipping meme commission",
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	if referralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("User has no referrer, skipping meme commission",
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	// Use commissionAmountUSD as cashback amount for meme commission calculation
	cashbackAmountUSD := commissionAmountUSD
	// Use PlatformFeeAmount as cashback amount SOL for meme commission calculation
	cashbackAmountSOL := affiliateTx.PlatformFeeAmount

	global.GVA_LOG.Info("Processing meme commission",
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("cashback_amount_usd", cashbackAmountUSD.String()),
		zap.String("cashback_amount_sol", cashbackAmountSOL.String()))

	// Get the upline hierarchy for meme commission distribution
	err = s.getUplineHierarchyForMeme(ctx, affiliateTx.UserID, affiliateTx, cashbackAmountUSD, cashbackAmountSOL)
	if err != nil {
		return fmt.Errorf("failed to process meme commission hierarchy: %w", err)
	}

	return nil
}

// getUplineHierarchyForMeme gets the upline hierarchy for meme commission distribution (up to 3 levels)
func (s *MemeCommissionService) getUplineHierarchyForMeme(ctx context.Context, userID uuid.UUID, affiliateTx *model.AffiliateTransaction, cashbackAmountUSD decimal.Decimal, cashbackAmountSOL decimal.Decimal) error {
	// Get direct referrer (L1)
	referralInfo, err := s.getReferralInfo(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get referral info: %w", err)
	}

	if referralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("No L1 referrer found for meme commission",
			zap.String("user_id", userID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	// Get L1 upline
	l1User, err := s.getUserWithLevel(ctx, *referralInfo.ReferrerID)
	if err != nil {
		return fmt.Errorf("failed to get L1 user with level: %w", err)
	}

	if l1User == nil {
		global.GVA_LOG.Debug("No L1 user found for referral",
			zap.String("referrer_id", referralInfo.ReferrerID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	// Create direct commission for L1
	err = s.createMemeCommission(ctx, affiliateTx, l1User, "Direct", cashbackAmountUSD, cashbackAmountSOL)
	if err != nil {
		return fmt.Errorf("failed to create direct meme commission: %w", err)
	}

	// Get L2 upline (referrer of L1)
	l2ReferralInfo, err := s.getReferralInfo(ctx, *referralInfo.ReferrerID)
	if err != nil {
		global.GVA_LOG.Debug("No L2 referrer found for meme commission",
			zap.String("user_id", userID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	if l2ReferralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("No L2 referrer found for meme commission",
			zap.String("user_id", userID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	l2User, err := s.getUserWithLevel(ctx, *l2ReferralInfo.ReferrerID)
	if err != nil {
		return fmt.Errorf("failed to get L2 user with level: %w", err)
	}

	if l2User == nil {
		global.GVA_LOG.Debug("No L2 user found for referral",
			zap.String("referrer_id", l2ReferralInfo.ReferrerID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	// Create indirect commission for L2
	err = s.createMemeCommission(ctx, affiliateTx, l2User, "Indirect", cashbackAmountUSD, cashbackAmountSOL)
	if err != nil {
		return fmt.Errorf("failed to create indirect meme commission: %w", err)
	}

	// Get L3 upline (referrer of L2)
	l3ReferralInfo, err := s.getReferralInfo(ctx, *l2ReferralInfo.ReferrerID)
	if err != nil || l3ReferralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("No L3 referrer found for meme commission",
			zap.String("user_id", userID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	l3User, err := s.getUserWithLevel(ctx, *l3ReferralInfo.ReferrerID)
	if err != nil {
		return fmt.Errorf("failed to get L3 user with level: %w", err)
	}

	if l3User == nil {
		global.GVA_LOG.Debug("No L3 user found for referral",
			zap.String("referrer_id", l3ReferralInfo.ReferrerID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	// Create extended commission for L3
	err = s.createMemeCommission(ctx, affiliateTx, l3User, "Extended", cashbackAmountUSD, cashbackAmountSOL)
	if err != nil {
		return fmt.Errorf("failed to create extended meme commission: %w", err)
	}

	return nil
}

func (s *MemeCommissionService) getUserWithLevel(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		Where("id = ?", userID).
		First(&user).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// createMemeCommission creates a meme commission record for the specified user
func (s *MemeCommissionService) createMemeCommission(ctx context.Context, affiliateTx *model.AffiliateTransaction, recipientUser *model.User, commissionType string, cashbackAmountUSD decimal.Decimal, cashbackAmountSOL decimal.Decimal) error {
	// Get agent level for commission rate
	agentLevel, err := s.levelRepo.GetAgentLevelByID(ctx, recipientUser.AgentLevelID)
	if err != nil {
		return fmt.Errorf("failed to get agent level: %w", err)
	}

	if agentLevel == nil {
		global.GVA_LOG.Warn("No agent level found for user, skipping meme commission",
			zap.String("user_id", recipientUser.ID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("commission_type", commissionType))
		return nil
	}

	// Calculate commission rate based on type
	var commissionRate decimal.Decimal
	switch commissionType {
	case "Direct":
		commissionRate = agentLevel.DirectCommissionRate
	case "Indirect":
		commissionRate = agentLevel.IndirectCommissionRate
	case "Extended":
		commissionRate = agentLevel.ExtendedCommissionRate
	default:
		return fmt.Errorf("invalid commission type: %s", commissionType)
	}

	// Check if commission record already exists
	var existingCommission model.MemeCommissionLedger
	err = global.GVA_DB.WithContext(ctx).
		Where("source_transaction_id = ? AND recipient_user_id = ? AND source_transaction_type = ?",
			affiliateTx.OrderID.String(), recipientUser.ID, commissionType).
		First(&existingCommission).Error

	if err == nil {
		// Record already exists, skip creation
		global.GVA_LOG.Info("Meme commission already exists",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("recipient_user_id", recipientUser.ID.String()),
			zap.String("commission_type", commissionType))
		return nil
	}

	// Calculate commission amount in USD
	// Use cashbackAmountUSD (platform fee) * commissionRate
	commissionAmountUSD := cashbackAmountUSD.Mul(commissionRate).Truncate(18)

	// Calculate commission amount in SOL
	// Use cashbackAmountSOL (based on PlatformFeeAmount) * commissionRate
	commissionAmountSOL := cashbackAmountSOL.Mul(commissionRate).Truncate(9)

	if commissionAmountUSD.LessThanOrEqual(decimal.Zero) {
		global.GVA_LOG.Warn("Calculated meme commission amount is zero or negative, skipping",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("recipient_user_id", recipientUser.ID.String()),
			zap.String("commission_type", commissionType),
			zap.String("cashback_amount_usd", cashbackAmountUSD.String()),
			zap.String("cashback_amount_sol", cashbackAmountSOL.String()),
			zap.String("commission_rate", commissionRate.String()))
		return nil
	}

	// Create commission record
	now := time.Now()
	// Convert commission rate to percentage string (e.g., 0.30 -> "30%")
	commissionRatePercent := commissionRate.Mul(decimal.NewFromInt(100)).StringFixed(2) + "%"

	commissionLedger := &model.MemeCommissionLedger{
		RecipientUserID:       recipientUser.ID,
		SourceUserID:          affiliateTx.UserID,
		SourceTransactionID:   affiliateTx.OrderID.String(),
		SourceTransactionType: commissionType,
		CommissionAmount:      commissionAmountUSD,
		CommissionAmountSol:   commissionAmountSOL,
		CommissionAsset:       commissionRatePercent, // Commission rate as percentage
		Status:                "PENDING_CLAIM",
		CreatedAt:             &now,
		UpdatedAt:             &now,
	}

	if err := s.memeCommissionRepo.CreateMemeCommissionLedger(ctx, commissionLedger); err != nil {
		return fmt.Errorf("failed to create meme commission record: %w", err)
	}

	global.GVA_LOG.Info("Created meme commission",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("recipient_user_id", recipientUser.ID.String()),
		zap.String("commission_type", commissionType),
		zap.String("commission_amount_usd", commissionAmountUSD.String()),
		zap.String("commission_amount_sol", commissionAmountSOL.String()),
		zap.String("commission_rate", commissionRate.String()))

	return nil
}

// getReferralInfo gets referral information for a user
func (s *MemeCommissionService) getReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := global.GVA_DB.WithContext(ctx).
		Where("user_id = ?", userID).
		First(&referral).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}
