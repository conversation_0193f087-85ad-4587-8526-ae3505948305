package contract

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
	"gorm.io/gorm"
)

type MockInvitationRepo struct {
	mock.Mock
}

func (m *MockInvitationRepo) Create(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockInvitationRepo) Update(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockInvitationRepo) CreateWallet(ctx context.Context, wallet *model.UserWallet) error {
	args := m.Called(ctx, wallet)
	return args.Error(0)
}

func (m *MockInvitationRepo) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.ReferralSnapshot), args.Error(1)
}

func (m *MockInvitationRepo) WithTransaction(ctx context.Context, fn func(ctx context.Context) (*model.User, error)) (*model.User, error) {
	args := m.Called(ctx, fn)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationRepo) GetByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationRepo) GetDirectReferral(ctx context.Context, userID, referrerID uuid.UUID) (*model.Referral, error) {
	args := m.Called(ctx, userID, referrerID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Referral), args.Error(1)
}

func (m *MockInvitationRepo) HasDirectReferral(ctx context.Context, userID uuid.UUID) (bool, error) {
	args := m.Called(ctx, userID)
	return args.Bool(0), args.Error(1)
}

func (m *MockInvitationRepo) IsInUpline(ctx context.Context, referrerID, userID uuid.UUID) (bool, error) {
	args := m.Called(ctx, referrerID, userID)
	return args.Bool(0), args.Error(1)
}

func (m *MockInvitationRepo) GetAllReferrals(ctx context.Context, tx *gorm.DB, userID uuid.UUID) ([]model.Referral, error) {
	args := m.Called(ctx, tx, userID)
	return args.Get(0).([]model.Referral), args.Error(1)
}

func (m *MockInvitationRepo) GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error) {
	args := m.Called(ctx, invitationCode)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationRepo) GetByInvitationCode(ctx context.Context, code string) (*model.User, error) {
	args := m.Called(ctx, code)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationRepo) CreateUserWallet(ctx context.Context, wallet *model.UserWallet) error {
	args := m.Called(ctx, wallet)
	return args.Error(0)
}

func (m *MockInvitationRepo) GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Referral), args.Error(1)
}

type MockLevelRepo struct {
	mock.Mock
}

func (m *MockLevelRepo) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.AgentLevel), args.Error(1)
}

func (m *MockLevelRepo) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.AgentLevel), args.Error(1)
}

func (m *MockLevelRepo) UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) error {
	args := m.Called(ctx, level)
	return args.Error(0)
}

type MockCommissionLedgerRepository struct {
	mock.Mock
}

func (m *MockCommissionLedgerRepository) GetClaimedAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetPendingClaimAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetClaimedAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error) {
	args := m.Called(ctx, userID, transactionType)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetPendingClaimAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error) {
	args := m.Called(ctx, userID, transactionType)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetRebateAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error) {
	args := m.Called(ctx, userID, transactionType, startTime, endTime)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}

func TestMain(m *testing.M) {
	test.SetupTestConfig()

	m.Run()

	test.CleanupTestConfig()
}

func TestNewContractCommissionService(t *testing.T) {
	service := NewContractCommissionService()

	assert.NotNil(t, service)
	assert.NotNil(t, service.userRepo)
	assert.NotNil(t, service.levelRepo)
	assert.NotNil(t, service.commissionRepo)
}

func TestContractCommissionService_ProcessContractCommission(t *testing.T) {
	tests := []struct {
		name             string
		transaction      *model.HyperLiquidTransaction
		expectedError    bool
		expectedErrorMsg string
	}{
		{
			name: "跳过非完成状态的交易",
			transaction: &model.HyperLiquidTransaction{
				Cloid:  "test_cloid_456",
				UserID: &uuid.UUID{},
				Status: stringPtr("pending"),
			},
			expectedError: false,
		},
		{
			name: "跳过已取消状态的交易",
			transaction: &model.HyperLiquidTransaction{
				Cloid:  "test_cloid_789",
				UserID: &uuid.UUID{},
				Status: stringPtr("cancelled"),
			},
			expectedError: false,
		},
		{
			name: "跳过失败状态的交易",
			transaction: &model.HyperLiquidTransaction{
				Cloid:  "test_cloid_101",
				UserID: &uuid.UUID{},
				Status: stringPtr("failed"),
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test environment with database
			_, _, _ = test.SetupTestWithDB(t)
			defer test.TeardownTest()

			service := NewContractCommissionService()

			err := service.ProcessContractCommission(context.Background(), tt.transaction)

			if tt.expectedError {
				assert.Error(t, err)
				if tt.expectedErrorMsg != "" {
					assert.Contains(t, err.Error(), tt.expectedErrorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestContractCommissionService_HelperFunctions(t *testing.T) {
	t.Run("测试辅助函数", func(t *testing.T) {
		testStr := "test_string"
		strPtr := stringPtr(testStr)
		assert.NotNil(t, strPtr)
		assert.Equal(t, testStr, *strPtr)

		testUUID := uuid.New()
		uuidPtr := uuidPtr(testUUID)
		assert.NotNil(t, uuidPtr)
		assert.Equal(t, testUUID, *uuidPtr)

		testDecimal := decimal.NewFromFloat(123.45)
		decimalPtr := decimalPtr(testDecimal)
		assert.NotNil(t, decimalPtr)
		assert.Equal(t, testDecimal, *decimalPtr)
	})
}

func TestContractCommissionService_TestDataGenerators(t *testing.T) {
	t.Run("测试数据生成器", func(t *testing.T) {
		cloid := "test_cloid_123"
		userID := uuid.New()
		status := "filled"
		buildFee := 100.0

		tx := createTestHyperLiquidTransaction(cloid, userID, status, buildFee)
		assert.Equal(t, cloid, tx.Cloid)
		assert.Equal(t, userID, *tx.UserID)
		assert.Equal(t, status, *tx.Status)
		assert.Equal(t, decimal.NewFromFloat(buildFee), *tx.BuildFee)

		agentLevelID := uint(2)
		user := createTestUser(userID, agentLevelID)
		assert.Equal(t, userID, user.ID)
		assert.Equal(t, agentLevelID, user.AgentLevelID)
		assert.NotNil(t, user.Email)
		assert.NotNil(t, user.InvitationCode)

		levelID := uint(3)
		levelName := "Lv3"
		directRate := 0.30
		indirectRate := 0.05
		extendedRate := 0.025

		level := createTestAgentLevel(levelID, levelName, directRate, indirectRate, extendedRate)
		assert.Equal(t, levelID, level.ID)
		assert.Equal(t, levelName, level.Name)
		assert.Equal(t, decimal.NewFromFloat(directRate), level.DirectCommissionRate)
		assert.Equal(t, decimal.NewFromFloat(indirectRate), level.IndirectCommissionRate)
		assert.Equal(t, decimal.NewFromFloat(extendedRate), level.ExtendedCommissionRate)

		referrerID := uuid.New()
		depth := 2
		referral := createTestReferral(userID, referrerID, depth)
		assert.Equal(t, userID, referral.UserID)
		assert.Equal(t, referrerID, *referral.ReferrerID)
		assert.Equal(t, depth, referral.Depth)
	})
}

func stringPtr(s string) *string {
	return &s
}

func uuidPtr(id uuid.UUID) *uuid.UUID {
	return &id
}

func decimalPtr(d decimal.Decimal) *decimal.Decimal {
	return &d
}

func createTestHyperLiquidTransaction(cloid string, userID uuid.UUID, status string, buildFee float64) *model.HyperLiquidTransaction {
	return &model.HyperLiquidTransaction{
		Cloid:    cloid,
		UserID:   &userID,
		Status:   &status,
		BuildFee: decimalPtr(decimal.NewFromFloat(buildFee)),
	}
}

func createTestUser(id uuid.UUID, agentLevelID uint) *model.User {
	email := "<EMAIL>"
	invitationCode := "TEST123"
	return &model.User{
		ID:             id,
		Email:          &email,
		InvitationCode: &invitationCode,
		AgentLevelID:   agentLevelID,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
}

func createTestAgentLevel(id uint, name string, directRate, indirectRate, extendedRate float64) *model.AgentLevel {
	return &model.AgentLevel{
		ID:                      id,
		Name:                    name,
		MemeVolumeThreshold:     decimal.NewFromFloat(1000.0),
		ContractVolumeThreshold: decimal.NewFromFloat(5000.0),
		MemeFeeRate:             decimal.NewFromFloat(0.008),
		TakerFeeRate:            decimal.NewFromFloat(0.0055),
		MakerFeeRate:            decimal.NewFromFloat(0.0025),
		DirectCommissionRate:    decimal.NewFromFloat(directRate),
		IndirectCommissionRate:  decimal.NewFromFloat(indirectRate),
		ExtendedCommissionRate:  decimal.NewFromFloat(extendedRate),
		MemeFeeRebate:           decimal.NewFromFloat(0.25),
	}
}

func createTestReferral(userID, referrerID uuid.UUID, depth int) *model.Referral {
	return &model.Referral{
		UserID:     userID,
		ReferrerID: &referrerID,
		Depth:      depth,
		CreatedAt:  time.Now(),
	}
}

// TestContractCommissionService_FirstTransactionReferralPoints tests that first perpetual trades trigger referral points
func TestContractCommissionService_FirstTransactionReferralPoints(t *testing.T) {
	t.Run("First perpetual trade should trigger referral points", func(t *testing.T) {
		// Setup test environment with database
		_, _, _ = test.SetupTestWithDB(t)
		defer test.TeardownTest()

		// Create test users
		referrerID := uuid.New()
		invitedUserID := uuid.New()

		// Create referrer user in database
		referrer := &model.User{
			ID:             referrerID,
			Email:          stringPtr("<EMAIL>"),
			InvitationCode: stringPtr("REF123"),
			AgentLevelID:   1,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}

		// Create invited user in database
		invitedUser := &model.User{
			ID:             invitedUserID,
			Email:          stringPtr("<EMAIL>"),
			InvitationCode: stringPtr("INV456"),
			AgentLevelID:   1,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}

		// Create referral relationship
		referral := &model.Referral{
			UserID:     invitedUserID,
			ReferrerID: &referrerID,
			Depth:      1,
			CreatedAt:  time.Now(),
		}

		// Create invite friends task
		inviteFriendsTask := &model.ActivityTask{
			ID:             uuid.New(),
			CategoryID:     2, // Community category
			Name:           "Invite Friends",
			TaskIdentifier: func() *model.TaskIdentifier { id := model.TaskIDInviteFriends; return &id }(),
			Points:         100,
			IsActive:       true,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}

		// Insert test data into database
		err := global.GVA_DB.Create(referrer).Error
		assert.NoError(t, err)

		err = global.GVA_DB.Create(invitedUser).Error
		assert.NoError(t, err)

		err = global.GVA_DB.Create(referral).Error
		assert.NoError(t, err)

		err = global.GVA_DB.Create(inviteFriendsTask).Error
		assert.NoError(t, err)

		// Create a filled perpetual transaction for the invited user (first transaction)
		tx := &model.HyperLiquidTransaction{
			Cloid:    "test_perpetual_tx_123",
			UserID:   &invitedUserID,
			Status:   stringPtr("filled"),
			BuildFee: decimalPtr(decimal.NewFromFloat(10.0)),
		}

		// Create service and process the transaction
		service := NewContractCommissionService()
		err = service.ProcessContractCommission(context.Background(), tx)
		assert.NoError(t, err)

		// Verify that the invited user's FirstTransactionAt was updated
		var updatedUser model.User
		err = global.GVA_DB.First(&updatedUser, "id = ?", invitedUserID).Error
		assert.NoError(t, err)
		assert.NotNil(t, updatedUser.FirstTransactionAt, "FirstTransactionAt should be set after first transaction")

		// Verify that an unlimited task completion was created for the referrer
		var taskCompletion model.UnlimitedTaskCompletion
		err = global.GVA_DB.Where("user_id = ? AND task_id = ?", referrerID, inviteFriendsTask.ID).First(&taskCompletion).Error
		assert.NoError(t, err, "Referrer should have a task completion record for invite friends task")
		assert.Equal(t, 100, taskCompletion.PointsAwarded, "Referrer should receive 100 points for invite friends task")

		t.Logf("✅ First perpetual trade successfully triggered referral points")
		t.Logf("✅ Invited user ID: %s", invitedUserID.String())
		t.Logf("✅ Referrer ID: %s", referrerID.String())
		t.Logf("✅ Points awarded: %d", taskCompletion.PointsAwarded)
	})
}
