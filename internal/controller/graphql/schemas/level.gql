# Level management schema

# LevelId only accepts values 6 and 7
scalar LevelId

input UpdateLevelCommissionInput {
  levelId: LevelId! # Only accepts values 6 and 7
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
  memeFeeRebate: Float!
}

type UpdateLevelCommissionResponse {
  level: AgentLevel
  success: Boolean!
  message: String!
}

# User level information query
type UserLevelInfo {
  currentLevel: AgentLevel!
  memeVolume: String!
  contractVolume: String!
  totalVolume: String!
}

type UserLevelInfoResponse {
  success: Boolean!
  message: String!
  data: UserLevelInfo
}
