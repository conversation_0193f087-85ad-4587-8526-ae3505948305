# Transaction data schema

type TransactionData {
  # 已领取返佣金额(USD)
  claimedUsd: String!
  # 待领取返佣金额(USD)
  pendingClaimUsd: String!
  # 衍生品交易量(USD) - 合约交易量
  contractVolumeUsd: String!
  # Meme交易量(USD)
  memeVolumeUsd: String!
  # 受邀人数
  invitationCount: Int!
  # 交易人数
  transactingUserCount: Int!
  # 总交易金额(USD) - 用于内部计算，保持向后兼容
  transactionAmountUsd: String!
}

enum TransactionDataType {
  ALL
  MEME
  CONTRACT
}

enum TimeRangeType {
  TODAY
  LAST_30_DAYS
  LAST_60_DAYS
  ALL_TIME
}

input TransactionDataInput {
  dataType: TransactionDataType!
  timeRange: TimeRangeType!
}

type TransactionDataResponse {
  transactionData: [TransactionData!]!
  success: Boolean!
  message: String
}
