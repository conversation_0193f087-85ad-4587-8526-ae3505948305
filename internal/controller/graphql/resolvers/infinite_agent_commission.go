package resolvers

import (
	"context"
	"fmt"
	"time"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/infinite"
	"go.uber.org/zap"
)

type InfiniteAgentCommissionResolver struct{}

func NewInfiniteAgentCommissionResolver() *InfiniteAgentCommissionResolver {
	return &InfiniteAgentCommissionResolver{}
}

// CreateInfiniteAgentCommission is the resolver for the createInfiniteAgentCommission field.
func (r *InfiniteAgentCommissionResolver) CreateInfiniteAgentCommission(ctx context.Context) (*gql_model.CreateInfiniteAgentCommissionResponse, error) {
	global.GVA_LOG.Info("Starting manual infinite agent commission calculation via GraphQL API")

	// Get current time as calculation date
	calculationDate := time.Now().UTC()
	calculationDateStr := calculationDate.Format("2006-01-02 15:04:05")

	global.GVA_LOG.Info("Calculation date", zap.String("date", calculationDateStr))

	// Create task instance
	task := infinite.NewInfiniteAgentCommissionTask()

	// Get all active infinite agent configurations
	activeInfiniteAgents, err := task.GetActiveInfiniteAgentConfigs()
	if err != nil {
		global.GVA_LOG.Error("Failed to get active infinite agents", zap.Error(err))
		return &gql_model.CreateInfiniteAgentCommissionResponse{
			Success:             false,
			Message:             fmt.Sprintf("Failed to get active infinite agents: %v", err),
			ProcessedCount:      0,
			ErrorCount:          0,
			TotalInfiniteAgents: 0,
			CalculationDate:     calculationDateStr,
		}, nil
	}

	global.GVA_LOG.Info("Found active infinite agents", zap.Int("infinite_agent_count", len(activeInfiniteAgents)))

	processedCount := 0
	errorCount := 0

	// Process commission calculation for each infinite agent
	for _, infiniteAgent := range activeInfiniteAgents {
		if err := task.ProcessInfiniteAgentCommission(infiniteAgent); err != nil {
			global.GVA_LOG.Error("Failed to process infinite agent commission calculation",
				zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
				zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	global.GVA_LOG.Info("Infinite agent commission calculation completed",
		zap.String("date", calculationDateStr),
		zap.Int("total_infinite_agents", len(activeInfiniteAgents)),
		zap.Int("processed_count", processedCount),
		zap.Int("error_count", errorCount))

	return &gql_model.CreateInfiniteAgentCommissionResponse{
		Success:             true,
		Message:             "Successfully completed infinite agent commission calculation",
		ProcessedCount:      processedCount,
		ErrorCount:          errorCount,
		TotalInfiniteAgents: len(activeInfiniteAgents),
		CalculationDate:     calculationDateStr,
	}, nil
}
