package resolvers

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/invitation"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/reward"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
)

type RewardResolver struct {
	rewardService           *reward.RewardDataService
	rewardClaimService      *reward.RewardClaimResultService
	invitationRecordService *invitation.InvitationRecordService
	logger                  *zap.Logger
}

func NewRewardResolver() *RewardResolver {
	return &RewardResolver{
		rewardService:           reward.NewRewardDataService(),
		rewardClaimService:      reward.NewRewardClaimResultService(),
		invitationRecordService: invitation.NewInvitationRecordService(),
		logger:                  global.GVA_LOG,
	}
}

func (r *RewardResolver) InvitationRecords(ctx context.Context, input gql_model.InvitationRecordRequest) (*gql_model.InvitationRecordResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.InvitationRecordResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	result, err := r.rewardService.GetInvitationRecords(ctx, userID, input.Page, input.PageSize)
	if err != nil {
		r.logger.Error("Failed to get invitation records", zap.Error(err))
		return &gql_model.InvitationRecordResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to obtain invitation records: %v", err),
		}, nil
	}

	var gqlRecords []*gql_model.InvitationRecord
	for _, record := range result.Records {
		gqlRecord := &gql_model.InvitationRecord{
			Address:           record.Address,
			TransactionVolume: record.TransactionVolume.String(),
			InvitedWithdrawal: record.InvitedWithdrawal.String(),
			Date:              record.Date,
			ChainID:           record.ChainID,
			Token:             record.Token,
		}
		gqlRecords = append(gqlRecords, gqlRecord)
	}

	// 按日期排序 (最新的在前)
	sort.Slice(gqlRecords, func(i, j int) bool {
		return gqlRecords[i].Date > gqlRecords[j].Date
	})

	return &gql_model.InvitationRecordResponse{
		Success:  true,
		Message:  "Get invitation records successfully",
		Data:     gqlRecords,
		Total:    int(result.Total),
		Page:     input.Page,
		PageSize: input.PageSize,
	}, nil
}

func (r *RewardResolver) WithdrawalRecords(ctx context.Context, input gql_model.WithdrawalRecordRequest) (*gql_model.WithdrawalRecordResponse, error) {
	// Get current user ID (from authentication info in context)
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		message := "Failed to get user information"
		return &gql_model.WithdrawalRecordResponse{
			Data:     []*gql_model.WithdrawalRecord{},
			Total:    0,
			Page:     input.Page,
			PageSize: input.PageSize,
			Success:  false,
			Message:  &message,
		}, nil
	}

	// Get withdrawal records from service layer
	result, err := r.rewardService.GetWithdrawalRecords(ctx, userID, input.Page, input.PageSize)
	if err != nil {
		r.logger.Error("Failed to get withdrawal records", zap.Error(err))
		message := "Failed to query withdrawal records"
		return &gql_model.WithdrawalRecordResponse{
			Data:     []*gql_model.WithdrawalRecord{},
			Total:    0,
			Page:     input.Page,
			PageSize: input.PageSize,
			Success:  false,
			Message:  &message,
		}, nil
	}

	// Convert service records to GraphQL format
	var withdrawalRecords []*gql_model.WithdrawalRecord
	for _, record := range result.Records {
		withdrawalRecords = append(withdrawalRecords, &gql_model.WithdrawalRecord{
			Hash:             record.Hash,
			WithdrawalReward: record.WithdrawalReward,
			Date:             record.Date,
			Type:             record.Type,
		})
	}

	// Sort by date (newest first)
	sort.Slice(withdrawalRecords, func(i, j int) bool {
		return withdrawalRecords[i].Date > withdrawalRecords[j].Date
	})

	message := "Query successful"
	return &gql_model.WithdrawalRecordResponse{
		Data:     withdrawalRecords,
		Total:    int(result.Total),
		Page:     input.Page,
		PageSize: input.PageSize,
		Success:  true,
		Message:  &message,
	}, nil
}

// RewardClaimHistory handles the reward claim history query
func (r *RewardResolver) RewardClaimHistory(ctx context.Context, input gql_model.RewardClaimHistoryRequest) (*gql_model.RewardClaimHistoryResponse, error) {
	// Get user ID from context
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	// Calculate offset for pagination
	offset := (input.Page - 1) * input.PageSize

	// Convert GraphQL enum to model enum if result filter is provided
	var resultFilter *model.RewardClaimResult
	if input.Result != nil {
		switch *input.Result {
		case gql_model.RewardClaimHistoryResultEnumPending:
			result := model.RewardClaimResultPending
			resultFilter = &result
		case gql_model.RewardClaimHistoryResultEnumProcessing:
			result := model.RewardClaimResultProcessing
			resultFilter = &result
		case gql_model.RewardClaimHistoryResultEnumSuccess:
			result := model.RewardClaimResultSuccess
			resultFilter = &result
		case gql_model.RewardClaimHistoryResultEnumFailed:
			result := model.RewardClaimResultFailed
			resultFilter = &result
		}
	}

	// Convert GraphQL string to model enum if type filter is provided
	isCashback := false
	if input.Type != nil {
		switch *input.Type {
		case "meme_cashback":
			isCashback = true
		case "meme_commission":
			isCashback = false
		}
	}

	if input.IsCashback != nil {
		isCashback = *input.IsCashback
	}

	// Get paginated reward claim results using service with optional filters
	records, totalCount, err := r.rewardClaimService.GetUserClaimHistory(userID, input.PageSize, offset, isCashback, resultFilter)
	if err != nil {
		r.logger.Error("Failed to get reward claim history", zap.Error(err))
		return nil, fmt.Errorf("Failed to get reward claim history")
	}

	// Convert model records to GraphQL format
	var gqlRecords []*gql_model.RewardClaimHistoryRecord
	for _, record := range records {
		gqlRecord := &gql_model.RewardClaimHistoryRecord{
			ID:        record.ID.String(),
			UserID:    record.UserID.String(),
			Address:   record.Address,
			Amount:    record.Amount.String(),
			AmountUsd: record.AmountUsd.String(),
			Token:     record.Token,
			ChainID:   record.ChainID,
			Type:      string(record.Type),
			Result:    string(record.Result),
			CreatedAt: record.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
			UpdatedAt: record.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		}

		// Handle optional fields
		if record.TransactionHash != nil {
			gqlRecord.TransactionHash = record.TransactionHash
		}
		if record.OnchainTimestamp != nil {
			timestamp := time.Unix(*record.OnchainTimestamp, 0).Format("2006-01-02T15:04:05Z07:00")
			gqlRecord.OnchainTimestamp = &timestamp
		}
		if record.ErrorMessage != nil {
			gqlRecord.ErrorMessage = record.ErrorMessage
		}
		if record.ErrorCode != nil {
			gqlRecord.ErrorCode = record.ErrorCode
		}
		if record.ProcessedAt != nil {
			processedAt := record.ProcessedAt.Format("2006-01-02T15:04:05Z07:00")
			gqlRecord.ProcessedAt = &processedAt
		}

		gqlRecords = append(gqlRecords, gqlRecord)
	}

	return &gql_model.RewardClaimHistoryResponse{
		Data:     gqlRecords,
		Total:    int(totalCount),
		Page:     input.Page,
		PageSize: input.PageSize,
		Success:  true,
	}, nil
}
