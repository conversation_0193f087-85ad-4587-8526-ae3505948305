package resolvers

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

// MockLevelService is a mock implementation of service.LevelI
type MockLevelService struct {
	mock.Mock
}

func (m *MockLevelService) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.AgentLevel), args.Error(1)
}

func (m *MockLevelService) CreateAgentLevel(ctx context.Context, level *model.AgentLevel) (*model.AgentLevel, error) {
	args := m.Called(ctx, level)
	return args.Get(0).(*model.AgentLevel), args.Error(1)
}

func (m *MockLevelService) UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) (*model.AgentLevel, error) {
	args := m.Called(ctx, level)
	return args.Get(0).(*model.AgentLevel), args.Error(1)
}

func (m *MockLevelService) DeleteAgentLevel(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockLevelService) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.AgentLevel), args.Error(1)
}

func (m *MockLevelService) GetUserLevelInfo(ctx context.Context, userID uuid.UUID) (*response.UserLevelInfoResponse, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*response.UserLevelInfoResponse), args.Error(1)
}

func (m *MockLevelService) UpdateLevelCommission(ctx context.Context, levelID uint, directRate, indirectRate, extendedRate, memeFeeRebate float64) (*model.AgentLevel, error) {
	args := m.Called(ctx, levelID, directRate, indirectRate, extendedRate, memeFeeRebate)
	return args.Get(0).(*model.AgentLevel), args.Error(1)
}

func TestLevelResolver_AgentLevels(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	fixtures := test.NewTestFixtures()
	helper := test.NewTestHelper(t)

	tests := []struct {
		name           string
		mockSetup      func(*MockLevelService)
		expectedLevels []model.AgentLevel
		expectedError  error
	}{
		{
			name: "successful retrieval of agent levels",
			mockSetup: func(mockService *MockLevelService) {
				levels := []model.AgentLevel{
					*fixtures.CreateTestAgentLevelWithID(1, "Lv1"),
					*fixtures.CreateTestAgentLevelWithID(2, "Lv2"),
					*fixtures.CreateTestAgentLevelWithID(3, "Lv3"),
				}
				mockService.On("GetAgentLevels", mock.Anything).Return(levels, nil)
			},
			expectedLevels: []model.AgentLevel{
				*fixtures.CreateTestAgentLevelWithID(1, "Lv1"),
				*fixtures.CreateTestAgentLevelWithID(2, "Lv2"),
				*fixtures.CreateTestAgentLevelWithID(3, "Lv3"),
			},
			expectedError: nil,
		},
		{
			name: "service error",
			mockSetup: func(mockService *MockLevelService) {
				mockService.On("GetAgentLevels", mock.Anything).Return([]model.AgentLevel{}, errors.New("database error"))
			},
			expectedLevels: []model.AgentLevel{},
			expectedError:  errors.New("failed to get agent levels: database error"),
		},
		{
			name: "empty result",
			mockSetup: func(mockService *MockLevelService) {
				mockService.On("GetAgentLevels", mock.Anything).Return([]model.AgentLevel{}, nil)
			},
			expectedLevels: []model.AgentLevel{},
			expectedError:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service
			mockService := &MockLevelService{}
			tt.mockSetup(mockService)

			// Create resolver with mock service
			resolver := &LevelResolver{
				s: mockService,
			}

			// Execute test
			ctx := helper.CreateTestContext()
			result, err := resolver.AgentLevels(ctx)

			// Verify results
			if tt.expectedError != nil {
				helper.AssertError(err)
				helper.AssertContains(err.Error(), "failed to get agent levels")
				helper.AssertNil(result)
			} else {
				helper.AssertNoError(err)
				if len(tt.expectedLevels) == 0 {
					// For empty results, result can be nil or empty slice
					if result != nil {
						helper.AssertLen(result, 0)
					}
				} else {
					helper.AssertNotNil(result)
					helper.AssertLen(result, len(tt.expectedLevels))
				}

				// Verify each level was converted correctly
				for i, expectedLevel := range tt.expectedLevels {
					if i < len(result) {
						helper.AssertEqual(int(expectedLevel.ID), result[i].ID)
						helper.AssertEqual(expectedLevel.Name, result[i].Name)
					}
				}
			}

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestLevelResolver_AgentLevel(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	fixtures := test.NewTestFixtures()
	helper := test.NewTestHelper(t)

	tests := []struct {
		name          string
		levelID       int
		mockSetup     func(*MockLevelService)
		expectedLevel *model.AgentLevel
		expectedError error
	}{
		{
			name:    "successful retrieval by ID",
			levelID: 1,
			mockSetup: func(mockService *MockLevelService) {
				level := fixtures.CreateTestAgentLevelWithID(1, "Lv1")
				mockService.On("GetAgentLevelByID", mock.Anything, uint(1)).Return(level, nil)
			},
			expectedLevel: fixtures.CreateTestAgentLevelWithID(1, "Lv1"),
			expectedError: nil,
		},
		{
			name:    "level not found",
			levelID: 999,
			mockSetup: func(mockService *MockLevelService) {
				mockService.On("GetAgentLevelByID", mock.Anything, uint(999)).Return((*model.AgentLevel)(nil), errors.New("level not found"))
			},
			expectedLevel: nil,
			expectedError: errors.New("failed to get agent level: level not found"),
		},
		{
			name:    "service error",
			levelID: 1,
			mockSetup: func(mockService *MockLevelService) {
				mockService.On("GetAgentLevelByID", mock.Anything, uint(1)).Return((*model.AgentLevel)(nil), errors.New("database error"))
			},
			expectedLevel: nil,
			expectedError: errors.New("failed to get agent level: database error"),
		},
		{
			name:    "invalid level ID - zero",
			levelID: 0,
			mockSetup: func(mockService *MockLevelService) {
				mockService.On("GetAgentLevelByID", mock.Anything, uint(0)).Return((*model.AgentLevel)(nil), errors.New("invalid level ID"))
			},
			expectedLevel: nil,
			expectedError: errors.New("failed to get agent level: invalid level ID"),
		},
		{
			name:    "negative level ID converted to uint",
			levelID: -1,
			mockSetup: func(mockService *MockLevelService) {
				// -1 converted to uint becomes a very large number
				mockService.On("GetAgentLevelByID", mock.Anything, mock.AnythingOfType("uint")).Return((*model.AgentLevel)(nil), errors.New("invalid level ID"))
			},
			expectedLevel: nil,
			expectedError: errors.New("failed to get agent level: invalid level ID"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service
			mockService := &MockLevelService{}
			tt.mockSetup(mockService)

			// Create resolver with mock service
			resolver := &LevelResolver{
				s: mockService,
			}

			// Execute test
			ctx := helper.CreateTestContext()
			result, err := resolver.AgentLevel(ctx, tt.levelID)

			// Verify results
			if tt.expectedError != nil {
				helper.AssertError(err)
				helper.AssertContains(err.Error(), "failed to get agent level")
				helper.AssertNil(result)
			} else {
				helper.AssertNoError(err)
				helper.AssertNotNil(result)
				if tt.expectedLevel != nil {
					helper.AssertEqual(int(tt.expectedLevel.ID), result.ID)
					helper.AssertEqual(tt.expectedLevel.Name, result.Name)
				}
			}

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestModelAgentLevelToGQL(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	fixtures := test.NewTestFixtures()
	helper := test.NewTestHelper(t)

	tests := []struct {
		name          string
		inputLevel    *model.AgentLevel
		expectedError bool
	}{
		{
			name:          "successful conversion",
			inputLevel:    fixtures.CreateTestAgentLevelWithID(1, "Lv1"),
			expectedError: false,
		},
		{
			name:          "conversion with different level",
			inputLevel:    fixtures.CreateTestAgentLevelWithID(5, "Lv5"),
			expectedError: false,
		},
		{
			name:          "nil input should not panic",
			inputLevel:    nil,
			expectedError: true, // This will likely panic or return nil
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.expectedError {
				// Test that nil input is handled gracefully
				defer func() {
					if r := recover(); r != nil {
						// Panic is expected for nil input
						helper.AssertNotNil(r)
					}
				}()
			}

			// Execute test
			result := ModelAgentLevelToGQL(tt.inputLevel)

			if !tt.expectedError {
				// Verify successful conversion
				helper.AssertNotNil(result)
				helper.AssertEqual(int(tt.inputLevel.ID), result.ID)
				helper.AssertEqual(tt.inputLevel.Name, result.Name)

				// Verify decimal conversions
				memeVolumeThreshold, _ := tt.inputLevel.MemeVolumeThreshold.Float64()
				helper.AssertEqual(memeVolumeThreshold, result.MemeVolumeThreshold)

				contractVolumeThreshold, _ := tt.inputLevel.ContractVolumeThreshold.Float64()
				helper.AssertEqual(contractVolumeThreshold, result.ContractVolumeThreshold)

				memeFeeRate, _ := tt.inputLevel.MemeFeeRate.Float64()
				helper.AssertEqual(memeFeeRate, result.MemeFeeRate)

				directCommissionRate, _ := tt.inputLevel.DirectCommissionRate.Float64()
				helper.AssertEqual(directCommissionRate, result.DirectCommissionRate)

				indirectCommissionRate, _ := tt.inputLevel.IndirectCommissionRate.Float64()
				helper.AssertEqual(indirectCommissionRate, result.IndirectCommissionRate)

				extendedCommissionRate, _ := tt.inputLevel.ExtendedCommissionRate.Float64()
				helper.AssertEqual(extendedCommissionRate, result.ExtendedCommissionRate)

				memeFeeRebate, _ := tt.inputLevel.MemeFeeRebate.Float64()
				helper.AssertEqual(memeFeeRebate, result.MemeFeeRebate)
			}
		})
	}
}

func TestLevelResolver_Integration(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	fixtures := test.NewTestFixtures()
	helper := test.NewTestHelper(t)

	t.Run("full workflow - get all levels then get specific level", func(t *testing.T) {
		// Create mock service
		mockService := &MockLevelService{}

		// Setup mock for getting all levels
		levels := []model.AgentLevel{
			*fixtures.CreateTestAgentLevelWithID(1, "Lv1"),
			*fixtures.CreateTestAgentLevelWithID(2, "Lv2"),
		}
		mockService.On("GetAgentLevels", mock.Anything).Return(levels, nil)

		// Setup mock for getting specific level
		specificLevel := fixtures.CreateTestAgentLevelWithID(1, "Lv1")
		mockService.On("GetAgentLevelByID", mock.Anything, uint(1)).Return(specificLevel, nil)

		// Create resolver with mock service
		resolver := &LevelResolver{
			s: mockService,
		}

		ctx := helper.CreateTestContext()

		// First, get all levels
		allLevels, err := resolver.AgentLevels(ctx)
		helper.AssertNoError(err)
		helper.AssertLen(allLevels, 2)

		// Then, get a specific level
		specificLevelResult, err := resolver.AgentLevel(ctx, 1)
		helper.AssertNoError(err)
		helper.AssertNotNil(specificLevelResult)
		helper.AssertEqual(1, specificLevelResult.ID)
		helper.AssertEqual("Lv1", specificLevelResult.Name)

		// Verify mock expectations
		mockService.AssertExpectations(t)
	})
}
