package resolvers

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/data_overview"
)

type DataOverviewResolver struct {
	s data_overview.DataOverviewServiceInterface
}

func NewDataOverviewResolver() *DataOverviewResolver {
	return &DataOverviewResolver{
		s: data_overview.NewDataOverviewService(),
	}
}

// RebateAmountChart handles the rebate amount chart query
func (d *DataOverviewResolver) RebateAmountChart(ctx context.Context, input gql_model.DataOverviewInput) (*gql_model.RebateAmountChartResponse, error) {
	// Get user ID from context
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return nil, fmt.Errorf("user ID not found in context")
	}

	// Convert GraphQL enum to string
	timeRange := string(input.TimeRange)

	// Get rebate amount chart data
	chartResponse, err := d.s.GetRebateAmountChart(ctx, userID, timeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to get rebate amount chart: %w", err)
	}

	// Convert to GraphQL response
	var message *string
	if chartResponse.Message != "" {
		message = &chartResponse.Message
	}

	response := &gql_model.RebateAmountChartResponse{
		CurrentValues: DTORebateAmountCurrentValuesToGQL(chartResponse.CurrentValues),
		Data:          DTORebateAmountChartDataPointListToGQL(chartResponse.Data),
		Success:       chartResponse.Success,
		Message:       message,
	}

	return response, nil
}

// TransactionVolumeChart handles the transaction volume chart query
func (d *DataOverviewResolver) TransactionVolumeChart(ctx context.Context, input gql_model.DataOverviewInput) (*gql_model.TransactionVolumeChartResponse, error) {
	// Get user ID from context
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return nil, fmt.Errorf("user ID not found in context")
	}

	// Convert GraphQL enum to string
	timeRange := string(input.TimeRange)

	// Get transaction volume chart data
	chartResponse, err := d.s.GetTransactionVolumeChart(ctx, userID, timeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction volume chart: %w", err)
	}

	// Convert to GraphQL response
	var message *string
	if chartResponse.Message != "" {
		message = &chartResponse.Message
	}

	response := &gql_model.TransactionVolumeChartResponse{
		CurrentValues: DTOTransactionVolumeCurrentValuesToGQL(chartResponse.CurrentValues),
		Data:          DTOTransactionVolumeChartDataPointListToGQL(chartResponse.Data),
		Success:       chartResponse.Success,
		Message:       message,
	}

	return response, nil
}

// InvitationCountChart handles the invitation count chart query
func (d *DataOverviewResolver) InvitationCountChart(ctx context.Context, input gql_model.DataOverviewInput) (*gql_model.InvitationCountChartResponse, error) {
	// Get user ID from context
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return nil, fmt.Errorf("user ID not found in context")
	}

	// Convert GraphQL enum to string
	timeRange := string(input.TimeRange)

	// Get invitation count chart data
	chartResponse, err := d.s.GetInvitationCountChart(ctx, userID, timeRange)
	if err != nil {
		return nil, fmt.Errorf("failed to get invitation count chart: %w", err)
	}

	// Convert to GraphQL response
	var message *string
	if chartResponse.Message != "" {
		message = &chartResponse.Message
	}

	response := &gql_model.InvitationCountChartResponse{
		CurrentValues: DTOInvitationCountCurrentValuesToGQL(chartResponse.CurrentValues),
		All:           DTOInvitationCountChartDataPointListToGQL(chartResponse.All),
		Success:       chartResponse.Success,
		Message:       message,
	}

	return response, nil
}

// DTORebateAmountChartDataPointListToGQL converts DTO chart data point list to GraphQL model list
func DTORebateAmountChartDataPointListToGQL(dataList []*response.RebateAmountChartDataPoint) []*gql_model.RebateAmountChartDataPoint {
	if dataList == nil {
		return nil
	}

	var result []*gql_model.RebateAmountChartDataPoint
	for _, data := range dataList {
		if data != nil {
			result = append(result, &gql_model.RebateAmountChartDataPoint{
				Timestamp: data.Timestamp,
				Period:    data.Period,
				Contract:  data.Contract.String(),
				Meme:      data.Meme.String(),
				All:       data.All.String(),
			})
		}
	}

	return result
}

// DTORebateAmountCurrentValuesToGQL converts DTO current values to GraphQL model
func DTORebateAmountCurrentValuesToGQL(currentValues *response.RebateAmountCurrentValues) *gql_model.RebateAmountCurrentValues {
	if currentValues == nil {
		return nil
	}

	return &gql_model.RebateAmountCurrentValues{
		Contract: currentValues.Contract.String(),
		Meme:     currentValues.Meme.String(),
		All:      currentValues.All.String(),
	}
}

// DTOTransactionVolumeChartDataPointListToGQL converts DTO transaction volume chart data point list to GraphQL model list
func DTOTransactionVolumeChartDataPointListToGQL(dataList []*response.TransactionVolumeChartDataPoint) []*gql_model.TransactionVolumeChartDataPoint {
	if dataList == nil {
		return nil
	}

	var result []*gql_model.TransactionVolumeChartDataPoint
	for _, data := range dataList {
		if data != nil {
			result = append(result, &gql_model.TransactionVolumeChartDataPoint{
				Timestamp: data.Timestamp,
				Period:    data.Period,
				Contract:  data.Contract.String(),
				Meme:      data.Meme.String(),
				All:       data.All.String(),
			})
		}
	}

	return result
}

// DTOTransactionVolumeCurrentValuesToGQL converts DTO transaction volume current values to GraphQL model
func DTOTransactionVolumeCurrentValuesToGQL(currentValues *response.TransactionVolumeCurrentValues) *gql_model.TransactionVolumeCurrentValues {
	if currentValues == nil {
		return nil
	}

	return &gql_model.TransactionVolumeCurrentValues{
		Contract: currentValues.Contract.String(),
		Meme:     currentValues.Meme.String(),
		All:      currentValues.All.String(),
	}
}

// DTOInvitationCountChartDataPointListToGQL converts DTO invitation count chart data point list to GraphQL model list
func DTOInvitationCountChartDataPointListToGQL(dataList []*response.InvitationCountChartDataPoint) []*gql_model.InvitationCountChartDataPoint {
	if dataList == nil {
		return nil
	}

	var result []*gql_model.InvitationCountChartDataPoint
	for _, data := range dataList {
		if data != nil {
			result = append(result, &gql_model.InvitationCountChartDataPoint{
				Timestamp: data.Timestamp,
				Period:    data.Period,
				Value:     fmt.Sprintf("%d", data.Value),
			})
		}
	}

	return result
}

// DTOInvitationCountCurrentValuesToGQL converts DTO invitation count current values to GraphQL model
func DTOInvitationCountCurrentValuesToGQL(currentValues *response.InvitationCountCurrentValues) *gql_model.InvitationCountCurrentValues {
	if currentValues == nil {
		return nil
	}

	return &gql_model.InvitationCountCurrentValues{
		All: fmt.Sprintf("%d", currentValues.All),
	}
}
