package response

import (
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// UserLevelInfo represents user's current level information
type UserLevelInfo struct {
	CurrentLevel              *model.AgentLevel `json:"currentLevel"`
	MemeVolume                decimal.Decimal   `json:"memeVolume"`
	ContractVolume            decimal.Decimal   `json:"contractVolume"`
	TotalVolume               decimal.Decimal   `json:"totalVolume"`
}

// UserLevelInfoResponse represents the response for user level info query
type UserLevelInfoResponse struct {
	Success bool           `json:"success"`
	Message string         `json:"message"`
	Data    *UserLevelInfo `json:"data"`
}
