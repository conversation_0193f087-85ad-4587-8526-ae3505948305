package response

type ClaimRewardResponse struct {
	ClaimMeme               string `json:"claimMeme"`
	ClaimMemeReferral       string `json:"claimMemeReferral"`
	ClaimContract           string `json:"claimContract"`
	TotalClaimedUsd         string `json:"totalClaimedUsd"`
	TotalClaimedReferralUsd string `json:"totalClaimedReferralUsd"`
}

type ReferralRewardResponse struct {
	ClaimMemeReferral   string `json:"claimMemeReferral"`
	ClaimAgentReferral  string `json:"claimAgentReferral"`
	TotalAccumulatedUSD string `json:"totalAccumulatedUSD"`
}
