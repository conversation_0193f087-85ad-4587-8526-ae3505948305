package response

import (
	"time"

	"github.com/shopspring/decimal"
)

// RebateAmountChartDataPoint represents a single data point for rebate amount chart
type RebateAmountChartDataPoint struct {
	Timestamp time.Time       `json:"timestamp"`
	Period    string          `json:"period"`
	Contract  decimal.Decimal `json:"contract"`
	Meme      decimal.Decimal `json:"meme"`
	All       decimal.Decimal `json:"all"`
}

// RebateAmountCurrentValues represents current values for rebate amount chart
type RebateAmountCurrentValues struct {
	All      decimal.Decimal `json:"all"`      // 全部(USDT)
	Meme     decimal.Decimal `json:"meme"`     // Meme(SOL)
	Contract decimal.Decimal `json:"contract"` // 合约(USDC)
}

// RebateAmountChartResponse represents the response for rebate amount chart query
type RebateAmountChartResponse struct {
	CurrentValues *RebateAmountCurrentValues    `json:"currentValues"`
	Data          []*RebateAmountChartDataPoint `json:"data"`
	Success       bool                          `json:"success"`
	Message       string                        `json:"message,omitempty"`
}

// DataOverviewCategory represents category-specific data
type DataOverviewCategory struct {
	All      decimal.Decimal `json:"all"`      // 全部
	Meme     decimal.Decimal `json:"meme"`     // Meme(SOL)
	Contract decimal.Decimal `json:"contract"` // 合约(USDC)
}

// TransactionVolumeChartDataPoint represents a single data point for transaction volume chart
type TransactionVolumeChartDataPoint struct {
	Timestamp time.Time       `json:"timestamp"`
	Period    string          `json:"period"`
	Contract  decimal.Decimal `json:"contract"`
	Meme      decimal.Decimal `json:"meme"`
	All       decimal.Decimal `json:"all"`
}

// TransactionVolumeCurrentValues represents current values for transaction volume chart
type TransactionVolumeCurrentValues struct {
	All      decimal.Decimal `json:"all"`      // 全部(USDT)
	Meme     decimal.Decimal `json:"meme"`     // Meme(SOL)
	Contract decimal.Decimal `json:"contract"` // 合约(USDC)
}

// TransactionVolumeChartResponse represents the response for transaction volume chart query
type TransactionVolumeChartResponse struct {
	CurrentValues *TransactionVolumeCurrentValues    `json:"currentValues"`
	Data          []*TransactionVolumeChartDataPoint `json:"data"`
	Success       bool                               `json:"success"`
	Message       string                             `json:"message,omitempty"`
}

// InvitationCountChartDataPoint represents a single data point for invitation count chart
type InvitationCountChartDataPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Period    string    `json:"period"`
	Value     int       `json:"value"`
}

// InvitationCountCurrentValues represents current values for invitation count chart
type InvitationCountCurrentValues struct {
	All int `json:"all"` // 累计邀请人数
}

// InvitationCountChartResponse represents the response for invitation count chart query
type InvitationCountChartResponse struct {
	CurrentValues *InvitationCountCurrentValues    `json:"currentValues"`
	All           []*InvitationCountChartDataPoint `json:"all"`
	Success       bool                             `json:"success"`
	Message       string                           `json:"message,omitempty"`
}
