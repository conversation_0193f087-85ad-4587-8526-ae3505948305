package resolvers

import (
	"context"
	"strconv"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/agent_referral_admin"
)

// AdminAgentReferralResolver handles admin agent referral GraphQL operations
type AdminAgentReferralResolver struct {
	service agent_referral_admin.AdminAgentReferralServiceInterface
}

// NewAdminAgentReferralResolver creates a new resolver instance
func NewAdminAgentReferralResolver() *AdminAgentReferralResolver {
	return &AdminAgentReferralResolver{
		service: agent_referral_admin.NewAdminAgentReferralService(),
	}
}

// Query Resolvers

// AdminGetInfiniteAgentById retrieves an infinite agent by ID
func (r *AdminAgentReferralResolver) AdminGetInfiniteAgentById(ctx context.Context, id string) (*gql_model.AdminInfiniteAgentConfig, error) {
	agentID, err := uuid.Parse(id)
	if err != nil {
		return nil, err
	}

	agent, err := r.service.GetInfiniteAgentById(ctx, agentID)
	if err != nil {
		return nil, err
	}

	return r.convertInfiniteAgentToGQL(agent), nil
}

// AdminGetAllInfiniteAgents retrieves all infinite agents with pagination
func (r *AdminAgentReferralResolver) AdminGetAllInfiniteAgents(ctx context.Context, input gql_model.GetInfiniteAgentsInput) (*gql_model.AdminInfiniteAgentsResponse, error) {
	serviceInput := &agent_referral_admin.GetInfiniteAgentsInput{
		Page:      1,
		PageSize:  10,
		SortOrder: "DESC",
	}

	if input.Page != nil {
		serviceInput.Page = *input.Page
	}
	if input.PageSize != nil {
		serviceInput.PageSize = *input.PageSize
	}
	if input.SortOrder != nil {
		serviceInput.SortOrder = *input.SortOrder
	}
	if input.Status != nil {
		serviceInput.Status = *input.Status
	}
	if input.SortBy != nil {
		serviceInput.SortBy = *input.SortBy
	}

	response, err := r.service.GetAllInfiniteAgents(ctx, serviceInput)
	if err != nil {
		return nil, err
	}

	var gqlAgents []*gql_model.AdminInfiniteAgentConfig
	for _, agent := range response.Agents {
		gqlAgents = append(gqlAgents, r.convertInfiniteAgentToGQL(agent))
	}

	return &gql_model.AdminInfiniteAgentsResponse{
		Agents:     gqlAgents,
		Total:      response.Total,
		Page:       response.Page,
		PageSize:   response.PageSize,
		TotalPages: response.TotalPages,
	}, nil
}

// AdminGetAllAgentLevels retrieves all agent levels with statistics
func (r *AdminAgentReferralResolver) AdminGetAllAgentLevels(ctx context.Context) ([]*gql_model.AgentLevelWithStats, error) {
	levels, err := r.service.GetAllAgentLevels(ctx)
	if err != nil {
		return nil, err
	}

	var gqlLevels []*gql_model.AgentLevelWithStats
	for _, level := range levels {
		memeVolumeThreshold, _ := level.AgentLevel.MemeVolumeThreshold.Float64()
		contractVolumeThreshold, _ := level.AgentLevel.ContractVolumeThreshold.Float64()
		memeFeeRate, _ := level.AgentLevel.MemeFeeRate.Float64()
		takerFeeRate, _ := level.AgentLevel.TakerFeeRate.Float64()
		makerFeeRate, _ := level.AgentLevel.MakerFeeRate.Float64()
		directCommissionRate, _ := level.AgentLevel.DirectCommissionRate.Float64()
		indirectCommissionRate, _ := level.AgentLevel.IndirectCommissionRate.Float64()
		extendedCommissionRate, _ := level.AgentLevel.ExtendedCommissionRate.Float64()
		memeFeeRebate, _ := level.AgentLevel.MemeFeeRebate.Float64()

		gqlLevel := &gql_model.AgentLevelWithStats{
			ID:                      int(level.AgentLevel.ID),
			Name:                    level.AgentLevel.Name,
			MemeVolumeThreshold:     memeVolumeThreshold,
			ContractVolumeThreshold: contractVolumeThreshold,
			MemeFeeRate:             memeFeeRate,
			TakerFeeRate:            takerFeeRate,
			MakerFeeRate:            makerFeeRate,
			DirectCommissionRate:    directCommissionRate,
			IndirectCommissionRate:  indirectCommissionRate,
			ExtendedCommissionRate:  extendedCommissionRate,
			MemeFeeRebate:           memeFeeRebate,
			UserCount:               level.UserCount,
			TotalVolumeUsd:          level.TotalVolumeUSD,
			TotalCommission:         level.TotalCommission,
		}
		gqlLevels = append(gqlLevels, gqlLevel)
	}

	return gqlLevels, nil
}

// AdminSearchUsersByInvitationCode searches users by invitation code
func (r *AdminAgentReferralResolver) AdminSearchUsersByInvitationCode(ctx context.Context, invitationCode string) ([]*gql_model.User, error) {
	users, err := r.service.SearchUsersByInvitationCode(ctx, invitationCode)
	if err != nil {
		return nil, err
	}

	var gqlUsers []*gql_model.User
	for _, user := range users {
		gqlUser := r.convertUserToGQL(user)
		gqlUsers = append(gqlUsers, gqlUser)
	}

	return gqlUsers, nil
}

// AdminGetUserReferralSnapshot retrieves user referral snapshot
func (r *AdminAgentReferralResolver) AdminGetUserReferralSnapshot(ctx context.Context, userID string) (*gql_model.ReferralSnapshotFull, error) {
	uid, err := uuid.Parse(userID)
	if err != nil {
		return nil, err
	}

	snapshot, err := r.service.GetUserReferralSnapshot(ctx, uid)
	if err != nil {
		return nil, err
	}

	return r.convertReferralSnapshotToGQLFull(snapshot), nil
}

// AdminGetUsersByAgentLevel retrieves users by agent level
func (r *AdminAgentReferralResolver) AdminGetUsersByAgentLevel(ctx context.Context, input gql_model.GetUsersByLevelInput) (*gql_model.UsersByLevelResponse, error) {
	serviceInput := &agent_referral_admin.GetUsersByLevelInput{
		LevelID:   input.LevelID,
		Page:      1,
		PageSize:  10,
		SortOrder: "DESC",
	}

	if input.Page != nil {
		serviceInput.Page = *input.Page
	}
	if input.PageSize != nil {
		serviceInput.PageSize = *input.PageSize
	}
	if input.SortOrder != nil {
		serviceInput.SortOrder = *input.SortOrder
	}
	if input.SortBy != nil {
		serviceInput.SortBy = *input.SortBy
	}

	response, err := r.service.GetUsersByAgentLevel(ctx, serviceInput)
	if err != nil {
		return nil, err
	}

	var gqlUsers []*gql_model.User
	for _, user := range response.Users {
		gqlUsers = append(gqlUsers, r.convertUserToGQL(user))
	}

	return &gql_model.UsersByLevelResponse{
		Users:      gqlUsers,
		Total:      response.Total,
		Page:       response.Page,
		PageSize:   response.PageSize,
		TotalPages: response.TotalPages,
	}, nil
}

// AdminGetAllReferralTrees retrieves all referral trees
func (r *AdminAgentReferralResolver) AdminGetAllReferralTrees(ctx context.Context, input gql_model.GetReferralTreesInput) (*gql_model.ReferralTreesResponse, error) {
	serviceInput := &agent_referral_admin.GetReferralTreesInput{
		Page:      1,
		PageSize:  10,
		SortOrder: "DESC",
	}

	if input.Page != nil {
		serviceInput.Page = *input.Page
	}
	if input.PageSize != nil {
		serviceInput.PageSize = *input.PageSize
	}
	if input.SortOrder != nil {
		serviceInput.SortOrder = *input.SortOrder
	}
	if input.SortBy != nil {
		serviceInput.SortBy = *input.SortBy
	}

	response, err := r.service.GetAllReferralTrees(ctx, serviceInput)
	if err != nil {
		return nil, err
	}

	var gqlTrees []*gql_model.InfiniteAgentReferralTree
	for _, tree := range response.Trees {
		gqlTree := r.convertReferralTreeToGQL(tree)
		gqlTrees = append(gqlTrees, gqlTree)
	}

	return &gql_model.ReferralTreesResponse{
		Trees:      gqlTrees,
		Total:      response.Total,
		Page:       response.Page,
		PageSize:   response.PageSize,
		TotalPages: response.TotalPages,
	}, nil
}

// AdminGetReferralTreeById retrieves a referral tree by ID
func (r *AdminAgentReferralResolver) AdminGetReferralTreeById(ctx context.Context, id string) (*gql_model.InfiniteAgentReferralTree, error) {
	treeID, err := uuid.Parse(id)
	if err != nil {
		return nil, err
	}

	tree, err := r.service.GetReferralTreeById(ctx, treeID)
	if err != nil {
		return nil, err
	}

	return r.convertReferralTreeToGQL(tree), nil
}

// AdminGetAgentReferralStats retrieves agent referral statistics
func (r *AdminAgentReferralResolver) AdminGetAgentReferralStats(ctx context.Context, input gql_model.AdminStatsDateInput) (*gql_model.AgentReferralStats, error) {
	stats, err := r.service.GetAgentReferralStats(ctx, input.StartDate, input.EndDate)
	if err != nil {
		return nil, err
	}

	return &gql_model.AgentReferralStats{
		TotalUsers:          int(stats.TotalUsers),
		TotalInfiniteAgents: int(stats.TotalInfiniteAgents),
		TotalVolumeUsd:      stats.TotalVolumeUSD,
		TotalCommissionPaid: stats.TotalCommissionPaid,
		ActiveReferralTrees: int(stats.ActiveReferralTrees),
		AverageTreeSize:     stats.AverageTreeSize,
	}, nil
}

// AdminGetCommissionDistributionStats retrieves commission distribution statistics
func (r *AdminAgentReferralResolver) AdminGetCommissionDistributionStats(ctx context.Context, input gql_model.AdminStatsDateInput) (*gql_model.CommissionDistributionStats, error) {
	stats, err := r.service.GetCommissionDistributionStats(ctx, input.StartDate, input.EndDate)
	if err != nil {
		return nil, err
	}

	return &gql_model.CommissionDistributionStats{
		DirectCommission:   stats.DirectCommission,
		IndirectCommission: stats.IndirectCommission,
		ExtendedCommission: stats.ExtendedCommission,
		InfiniteCommission: stats.InfiniteCommission,
		TotalCommission:    stats.TotalCommission,
	}, nil
}

// AdminGetTopPerformingAgents retrieves top performing agents
func (r *AdminAgentReferralResolver) AdminGetTopPerformingAgents(ctx context.Context, input gql_model.GetTopAgentsInput) ([]*gql_model.TopAgent, error) {
	serviceInput := &agent_referral_admin.GetTopAgentsInput{
		Limit:  10,
		SortBy: input.SortBy,
	}

	if input.Limit != nil {
		serviceInput.Limit = *input.Limit
	}

	agents, err := r.service.GetTopPerformingAgents(ctx, serviceInput)
	if err != nil {
		return nil, err
	}

	var gqlAgents []*gql_model.TopAgent
	for _, agent := range agents {
		gqlAgent := &gql_model.TopAgent{
			UserID:           agent.UserID.String(),
			InvitationCode:   agent.InvitationCode,
			Email:            &agent.Email,
			AgentLevel:       agent.AgentLevel,
			CommissionEarned: agent.CommissionEarned,
			VolumeGenerated:  agent.VolumeGenerated,
			ReferralCount:    agent.ReferralCount,
			IsInfiniteAgent:  agent.IsInfiniteAgent,
		}
		gqlAgents = append(gqlAgents, gqlAgent)
	}

	return gqlAgents, nil
}

// Mutation Resolvers

// AdminCreateInfiniteAgent creates a new infinite agent
func (r *AdminAgentReferralResolver) AdminCreateInfiniteAgent(ctx context.Context, input gql_model.CreateInfiniteAgentInput) (*gql_model.AdminInfiniteAgentConfig, error) {
	userID, err := uuid.Parse(input.UserID)
	if err != nil {
		return nil, err
	}

	serviceInput := &agent_referral_admin.CreateInfiniteAgentInput{
		UserID:          userID,
		CommissionRateN: input.CommissionRateN,
		Status:          input.Status,
	}

	agent, err := r.service.CreateInfiniteAgent(ctx, serviceInput)
	if err != nil {
		return nil, err
	}

	return r.convertInfiniteAgentToGQL(agent), nil
}

// AdminUpdateInfiniteAgent updates an infinite agent
func (r *AdminAgentReferralResolver) AdminUpdateInfiniteAgent(ctx context.Context, input gql_model.UpdateInfiniteAgentInput) (*gql_model.AdminInfiniteAgentConfig, error) {
	agentID, err := uuid.Parse(input.ID)
	if err != nil {
		return nil, err
	}

	serviceInput := &agent_referral_admin.UpdateInfiniteAgentInput{
		ID:              agentID,
		CommissionRateN: input.CommissionRateN,
		Status:          input.Status,
	}

	agent, err := r.service.UpdateInfiniteAgent(ctx, serviceInput)
	if err != nil {
		return nil, err
	}

	return r.convertInfiniteAgentToGQL(agent), nil
}

// AdminDeleteInfiniteAgent deletes an infinite agent
func (r *AdminAgentReferralResolver) AdminDeleteInfiniteAgent(ctx context.Context, id string) (bool, error) {
	agentID, err := uuid.Parse(id)
	if err != nil {
		return false, err
	}

	err = r.service.DeleteInfiniteAgent(ctx, agentID)
	if err != nil {
		return false, err
	}

	return true, nil
}

// AdminUpdateAgentLevelCommissionRates updates agent level commission rates
func (r *AdminAgentReferralResolver) AdminUpdateAgentLevelCommissionRates(ctx context.Context, input gql_model.UpdateCommissionRatesInput) (*gql_model.AgentLevelWithStats, error) {
	serviceInput := &agent_referral_admin.UpdateCommissionRatesInput{
		LevelID:                input.LevelID,
		DirectCommissionRate:   input.DirectCommissionRate,
		IndirectCommissionRate: input.IndirectCommissionRate,
		ExtendedCommissionRate: input.ExtendedCommissionRate,
	}

	level, err := r.service.UpdateAgentLevelCommissionRates(ctx, serviceInput)
	if err != nil {
		return nil, err
	}

	memeVolumeThreshold, _ := level.MemeVolumeThreshold.Float64()
	contractVolumeThreshold, _ := level.ContractVolumeThreshold.Float64()
	memeFeeRate, _ := level.MemeFeeRate.Float64()
	takerFeeRate, _ := level.TakerFeeRate.Float64()
	makerFeeRate, _ := level.MakerFeeRate.Float64()
	directCommissionRate, _ := level.DirectCommissionRate.Float64()
	indirectCommissionRate, _ := level.IndirectCommissionRate.Float64()
	extendedCommissionRate, _ := level.ExtendedCommissionRate.Float64()
	memeFeeRebate, _ := level.MemeFeeRebate.Float64()

	return &gql_model.AgentLevelWithStats{
		ID:                      int(level.ID),
		Name:                    level.Name,
		MemeVolumeThreshold:     memeVolumeThreshold,
		ContractVolumeThreshold: contractVolumeThreshold,
		MemeFeeRate:             memeFeeRate,
		TakerFeeRate:            takerFeeRate,
		MakerFeeRate:            makerFeeRate,
		DirectCommissionRate:    directCommissionRate,
		IndirectCommissionRate:  indirectCommissionRate,
		ExtendedCommissionRate:  extendedCommissionRate,
		MemeFeeRebate:           memeFeeRebate,
		UserCount:               0, // Would need additional query
		TotalVolumeUsd:          0, // Would need additional query
		TotalCommission:         0, // Would need additional query
	}, nil
}

// AdminCreateReferralTreeSnapshot creates a referral tree snapshot
func (r *AdminAgentReferralResolver) AdminCreateReferralTreeSnapshot(ctx context.Context, input gql_model.CreateTreeSnapshotInput) (*gql_model.InfiniteAgentReferralTree, error) {
	rootUserID, err := uuid.Parse(input.RootUserID)
	if err != nil {
		return nil, err
	}

	serviceInput := &agent_referral_admin.CreateTreeSnapshotInput{
		RootUserID: rootUserID,
	}

	tree, err := r.service.CreateReferralTreeSnapshot(ctx, serviceInput)
	if err != nil {
		return nil, err
	}

	return r.convertReferralTreeToGQL(tree), nil
}

// AdminDeleteReferralTree deletes a referral tree
func (r *AdminAgentReferralResolver) AdminDeleteReferralTree(ctx context.Context, id string) (bool, error) {
	treeID, err := uuid.Parse(id)
	if err != nil {
		return false, err
	}

	err = r.service.DeleteReferralTree(ctx, treeID)
	if err != nil {
		return false, err
	}

	return true, nil
}

// System Operation Resolvers

// AdminRecalculateAllReferralSnapshots triggers referral snapshot recalculation
func (r *AdminAgentReferralResolver) AdminRecalculateAllReferralSnapshots(ctx context.Context) (*gql_model.SystemOperationResult, error) {
	result, err := r.service.RecalculateAllReferralSnapshots(ctx)
	if err != nil {
		return nil, err
	}

	return &gql_model.SystemOperationResult{
		Success:        result.Success,
		ProcessedCount: result.ProcessedCount,
		ErrorCount:     result.ErrorCount,
		Message:        result.Message,
	}, nil
}

// AdminRecalculateAllInfiniteAgentCommissions triggers infinite agent commission recalculation
func (r *AdminAgentReferralResolver) AdminRecalculateAllInfiniteAgentCommissions(ctx context.Context) (*gql_model.SystemOperationResult, error) {
	result, err := r.service.RecalculateAllInfiniteAgentCommissions(ctx)
	if err != nil {
		return nil, err
	}

	return &gql_model.SystemOperationResult{
		Success:        result.Success,
		ProcessedCount: result.ProcessedCount,
		ErrorCount:     result.ErrorCount,
		Message:        result.Message,
	}, nil
}

// AdminSyncReferralTreeData triggers referral tree data synchronization
func (r *AdminAgentReferralResolver) AdminSyncReferralTreeData(ctx context.Context) (*gql_model.SystemOperationResult, error) {
	result, err := r.service.SyncReferralTreeData(ctx)
	if err != nil {
		return nil, err
	}

	return &gql_model.SystemOperationResult{
		Success:        result.Success,
		ProcessedCount: result.ProcessedCount,
		ErrorCount:     result.ErrorCount,
		Message:        result.Message,
	}, nil
}

// Helper Methods

// convertInfiniteAgentToGQL converts model to GraphQL type
func (r *AdminAgentReferralResolver) convertInfiniteAgentToGQL(agent *model.InfiniteAgentConfig) *gql_model.AdminInfiniteAgentConfig {
	commissionRateN, _ := agent.CommissionRateN.Float64()
	totalNetFeeUSD, _ := agent.TotalNetFeeUSD.Float64()
	totalStandardCommissionPaidUSD, _ := agent.TotalStandardCommissionPaidUSD.Float64()
	finalCommissionAmountUSD, _ := agent.FinalCommissionAmountUSD.Float64()
	memeTotalFeeUSD, _ := agent.MemeTotalFeeUSD.Float64()
	memePaidCommissionUSD, _ := agent.MemePaidCommissionUSD.Float64()
	memeNetFeeUSD, _ := agent.MemeNetFeeUSD.Float64()
	contractTotalFeeUSD, _ := agent.ContractTotalFeeUSD.Float64()
	contractPaidCommissionUSD, _ := agent.ContractPaidCommissionUSD.Float64()
	contractNetFeeUSD, _ := agent.ContractNetFeeUSD.Float64()

	return &gql_model.AdminInfiniteAgentConfig{
		ID:                             agent.ID.String(),
		UserID:                         agent.UserID.String(),
		CommissionRateN:                commissionRateN,
		TotalNetFeeUsd:                 totalNetFeeUSD,
		TotalStandardCommissionPaidUsd: totalStandardCommissionPaidUSD,
		FinalCommissionAmountUsd:       finalCommissionAmountUSD,
		MemeVolumeUsd:                  memeTotalFeeUSD,
		MemePaidCommissionUsd:          memePaidCommissionUSD,
		MemeNetFeeUsd:                  memeNetFeeUSD,
		ContractTotalFeeUsd:            contractTotalFeeUSD,
		ContractPaidCommissionUsd:      contractPaidCommissionUSD,
		ContractNetFeeUsd:              contractNetFeeUSD,
		Status:                         agent.Status,
		CreatedAt:                      agent.CreatedAt,
		UpdatedAt:                      agent.UpdatedAt,
	}
}

// convertUserToGQL converts user model to GraphQL type
func (r *AdminAgentReferralResolver) convertUserToGQL(user *model.User) *gql_model.User {
	gqlUser := &gql_model.User{
		ID:           user.ID.String(),
		AgentLevelID: int(user.AgentLevelID),
		CreatedAt:    user.CreatedAt.Format(time.RFC3339),
		UpdatedAt:    user.UpdatedAt.Format(time.RFC3339),
	}

	if user.Email != nil {
		gqlUser.Email = user.Email
	}
	if user.InvitationCode != nil {
		gqlUser.InvitationCode = user.InvitationCode
	}
	if user.DeletedAt.Valid {
		deletedAt := user.DeletedAt.Time.Format(time.RFC3339)
		gqlUser.DeletedAt = &deletedAt
	}
	if user.LevelGracePeriodStartedAt != nil {
		gracePeriod := user.LevelGracePeriodStartedAt.Format(time.RFC3339)
		gqlUser.LevelGracePeriodStartedAt = &gracePeriod
	}
	if user.LevelUpgradedAt != nil {
		upgraded := user.LevelUpgradedAt.Format(time.RFC3339)
		gqlUser.LevelUpgradedAt = &upgraded
	}
	if user.FirstTransactionAt != nil {
		firstTx := user.FirstTransactionAt.Format(time.RFC3339)
		gqlUser.FirstTransactionAt = &firstTx
	}

	return gqlUser
}

// convertReferralSnapshotToGQLFull converts referral snapshot to GraphQL ReferralSnapshotFull type
func (r *AdminAgentReferralResolver) convertReferralSnapshotToGQLFull(snapshot *model.ReferralSnapshot) *gql_model.ReferralSnapshotFull {
	totalVolumeUsd, _ := snapshot.TotalVolumeUSD.Float64()
	totalRewardsDistributed, _ := snapshot.TotalRewardsDistributed.Float64()
	totalPerpsVolumeUsd, _ := snapshot.TotalPerpsVolumeUSD.Float64()
	totalPerpsFees, _ := snapshot.TotalPerpsFees.Float64()
	totalPerpsFeesPaid, _ := snapshot.TotalPerpsFeesPaid.Float64()
	totalPerpsFeesUnpaid, _ := snapshot.TotalPerpsFeesUnPaid.Float64()
	totalMemeVolumeUsd, _ := snapshot.TotalMemeVolumeUSD.Float64()
	totalMemeFees, _ := snapshot.TotalMemeFees.Float64()
	totalMemeFeesPaid, _ := snapshot.TotalMemeFeesPaid.Float64()
	totalMemeFeesUnpaid, _ := snapshot.TotalMemeFeesUnPaid.Float64()
	totalCommissionEarnedUsd, _ := snapshot.TotalCommissionEarnedUSD.Float64()
	claimedCommissionUsd, _ := snapshot.ClaimedCommissionUSD.Float64()
	unclaimedCommissionUsd, _ := snapshot.UnclaimedCommissionUSD.Float64()
	totalCashbackEarnedUsd, _ := snapshot.TotalCashbackEarnedUSD.Float64()
	claimedCashbackUsd, _ := snapshot.ClaimedCashbackUSD.Float64()
	unclaimedCashbackUsd, _ := snapshot.UnclaimedCashbackUSD.Float64()

	result := &gql_model.ReferralSnapshotFull{
		UserID:                   snapshot.UserID.String(),
		DirectCount:              snapshot.DirectCount,
		TotalDownlineCount:       snapshot.TotalDownlineCount,
		TotalVolumeUsd:           totalVolumeUsd,
		TotalRewardsDistributed:  totalRewardsDistributed,
		TradingUserCount:         snapshot.TradingUserCount,
		TotalPerpsVolumeUsd:      totalPerpsVolumeUsd,
		TotalPerpsFees:           totalPerpsFees,
		TotalPerpsFeesPaid:       totalPerpsFeesPaid,
		TotalPerpsFeesUnpaid:     totalPerpsFeesUnpaid,
		TotalPerpsTradesCount:    int(snapshot.TotalPerpsTradesCount),
		TotalMemeVolumeUsd:       totalMemeVolumeUsd,
		TotalMemeFees:            totalMemeFees,
		TotalMemeFeesPaid:        totalMemeFeesPaid,
		TotalMemeFeesUnpaid:      totalMemeFeesUnpaid,
		TotalMemeTradesCount:     int(snapshot.TotalMemeTradesCount),
		TotalCommissionEarnedUsd: totalCommissionEarnedUsd,
		ClaimedCommissionUsd:     claimedCommissionUsd,
		UnclaimedCommissionUsd:   unclaimedCommissionUsd,
		TotalCashbackEarnedUsd:   totalCashbackEarnedUsd,
		ClaimedCashbackUsd:       claimedCashbackUsd,
		UnclaimedCashbackUsd:     unclaimedCashbackUsd,
	}

	if snapshot.L1UplineID != nil {
		l1UplineID := snapshot.L1UplineID.String()
		result.L1UplineID = &l1UplineID
	}
	if snapshot.L2UplineID != nil {
		l2UplineID := snapshot.L2UplineID.String()
		result.L2UplineID = &l2UplineID
	}
	if snapshot.L3UplineID != nil {
		l3UplineID := snapshot.L3UplineID.String()
		result.L3UplineID = &l3UplineID
	}

	return result
}

// convertReferralTreeToGQL converts referral tree to GraphQL type
func (r *AdminAgentReferralResolver) convertReferralTreeToGQL(tree *model.InfiniteAgentReferralTree) *gql_model.InfiniteAgentReferralTree {
	commissionRateN, _ := tree.CommissionRateN.Float64()
	totalVolumeUSD, _ := tree.TotalVolumeUSD.Float64()
	totalCommissionEarned, _ := tree.TotalCommissionEarned.Float64()

	return &gql_model.InfiniteAgentReferralTree{
		ID:                    strconv.Itoa(int(tree.ID)),
		CreatedAt:             tree.CreatedAt,
		InfiniteAgentUserID:   tree.InfiniteAgentUserID.String(),
		CommissionRateN:       commissionRateN,
		RootUserID:            tree.RootUserID.String(),
		SnapshotDate:          tree.SnapshotDate,
		TotalNodes:            tree.TotalNodes,
		MaxDepth:              tree.MaxDepth,
		DirectCount:           tree.DirectCount,
		ActiveUsers:           tree.ActiveUsers,
		TradingUsers:          tree.TradingUsers,
		TotalCommissionEarned: totalCommissionEarned,
		TotalVolumeUsd:        totalVolumeUSD,
		Status:                tree.Status,
	}
}
