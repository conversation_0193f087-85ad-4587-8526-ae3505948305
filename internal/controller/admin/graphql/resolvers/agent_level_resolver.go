package resolvers

import (
	"context"
	"fmt"

	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/level"
)

type AdminAgentLevelResolver struct {
	levelService service.LevelI
}

func NewAdminAgentLevelResolver() *AdminAgentLevelResolver {
	return &AdminAgentLevelResolver{
		levelService: level.NewLevelService(),
	}
}

// AdminCreateAgentLevel creates a new agent level
func (r *AdminAgentLevelResolver) AdminCreateAgentLevel(ctx context.Context, input gql_model.CreateAgentLevelInput) (*gql_model.CreateAgentLevelResponse, error) {
	level := &model.AgentLevel{
		Name:                    input.Name,
		MemeVolumeThreshold:     decimal.NewFromFloat(input.MemeVolumeThreshold),
		ContractVolumeThreshold: decimal.NewFromFloat(input.ContractVolumeThreshold),
		MemeFeeRate:             decimal.NewFromFloat(input.MemeFeeRate),
		TakerFeeRate:            decimal.NewFromFloat(input.TakerFeeRate),
		MakerFeeRate:            decimal.NewFromFloat(input.MakerFeeRate),
		DirectCommissionRate:    decimal.NewFromFloat(input.DirectCommissionRate),
		IndirectCommissionRate:  decimal.NewFromFloat(input.IndirectCommissionRate),
		ExtendedCommissionRate:  decimal.NewFromFloat(input.ExtendedCommissionRate),
		MemeFeeRebate:           decimal.NewFromFloat(input.MemeFeeRebate),
	}

	createdLevel, err := r.levelService.CreateAgentLevel(ctx, level)
	if err != nil {
		return &gql_model.CreateAgentLevelResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create agent level: %v", err),
			Level:   nil,
		}, nil
	}

	return &gql_model.CreateAgentLevelResponse{
		Level:   r.modelAgentLevelToGQL(createdLevel),
		Success: true,
		Message: "Agent level created successfully",
	}, nil
}

// AdminUpdateAgentLevel updates an existing agent level
func (r *AdminAgentLevelResolver) AdminUpdateAgentLevel(ctx context.Context, input gql_model.UpdateAgentLevelInput) (*gql_model.UpdateAgentLevelResponse, error) {
	level := &model.AgentLevel{
		ID:                      uint(input.ID),
		Name:                    input.Name,
		MemeVolumeThreshold:     decimal.NewFromFloat(input.MemeVolumeThreshold),
		ContractVolumeThreshold: decimal.NewFromFloat(input.ContractVolumeThreshold),
		MemeFeeRate:             decimal.NewFromFloat(input.MemeFeeRate),
		TakerFeeRate:            decimal.NewFromFloat(input.TakerFeeRate),
		MakerFeeRate:            decimal.NewFromFloat(input.MakerFeeRate),
		DirectCommissionRate:    decimal.NewFromFloat(input.DirectCommissionRate),
		IndirectCommissionRate:  decimal.NewFromFloat(input.IndirectCommissionRate),
		ExtendedCommissionRate:  decimal.NewFromFloat(input.ExtendedCommissionRate),
		MemeFeeRebate:           decimal.NewFromFloat(input.MemeFeeRebate),
	}

	updatedLevel, err := r.levelService.UpdateAgentLevel(ctx, level)
	if err != nil {
		return &gql_model.UpdateAgentLevelResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to update agent level: %v", err),
			Level:   nil,
		}, nil
	}

	return &gql_model.UpdateAgentLevelResponse{
		Level:   r.modelAgentLevelToGQL(updatedLevel),
		Success: true,
		Message: "Agent level updated successfully",
	}, nil
}

// AdminDeleteAgentLevel deletes an agent level
func (r *AdminAgentLevelResolver) AdminDeleteAgentLevel(ctx context.Context, id int) (*gql_model.DeleteAgentLevelResponse, error) {
	err := r.levelService.DeleteAgentLevel(ctx, uint(id))
	if err != nil {
		return &gql_model.DeleteAgentLevelResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to delete agent level: %v", err),
		}, nil
	}

	return &gql_model.DeleteAgentLevelResponse{
		Success: true,
		Message: "Agent level deleted successfully",
	}, nil
}

// modelAgentLevelToGQL converts model.AgentLevel to gql_model.AgentLevel
func (r *AdminAgentLevelResolver) modelAgentLevelToGQL(level *model.AgentLevel) *gql_model.AgentLevel {
	if level == nil {
		return nil
	}

	memeVolumeThreshold, _ := level.MemeVolumeThreshold.Float64()
	contractVolumeThreshold, _ := level.ContractVolumeThreshold.Float64()
	memeFeeRate, _ := level.MemeFeeRate.Float64()
	takerFeeRate, _ := level.TakerFeeRate.Float64()
	makerFeeRate, _ := level.MakerFeeRate.Float64()
	directCommissionRate, _ := level.DirectCommissionRate.Float64()
	indirectCommissionRate, _ := level.IndirectCommissionRate.Float64()
	extendedCommissionRate, _ := level.ExtendedCommissionRate.Float64()
	memeFeeRebate, _ := level.MemeFeeRebate.Float64()

	return &gql_model.AgentLevel{
		ID:                      int(level.ID),
		Name:                    level.Name,
		MemeVolumeThreshold:     memeVolumeThreshold,
		ContractVolumeThreshold: contractVolumeThreshold,
		MemeFeeRate:             memeFeeRate,
		TakerFeeRate:            takerFeeRate,
		MakerFeeRate:            makerFeeRate,
		DirectCommissionRate:    directCommissionRate,
		IndirectCommissionRate:  indirectCommissionRate,
		ExtendedCommissionRate:  extendedCommissionRate,
		MemeFeeRebate:           memeFeeRebate,
	}
}
