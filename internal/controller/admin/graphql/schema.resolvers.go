package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.78

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	admin_resolvers "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/resolvers"
)

// CreateTask is the resolver for the createTask field.
func (r *mutationResolver) CreateTask(ctx context.Context, input gql_model.CreateTaskInput) (*gql_model.ActivityTask, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.CreateTask(ctx, input)
}

// CreateConsecutiveCheckinTask is the resolver for the createConsecutiveCheckinTask field.
func (r *mutationResolver) CreateConsecutiveCheckinTask(ctx context.Context, input gql_model.CreateConsecutiveCheckinTaskInput) (*gql_model.ActivityTask, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.CreateConsecutiveCheckinTask(ctx, input)
}

// UpdateTask is the resolver for the updateTask field.
func (r *mutationResolver) UpdateTask(ctx context.Context, input gql_model.UpdateTaskInput) (*gql_model.ActivityTask, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.UpdateTask(ctx, input)
}

// DeleteTask is the resolver for the deleteTask field.
func (r *mutationResolver) DeleteTask(ctx context.Context, taskID string) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.DeleteTask(ctx, taskID)
}

// CreateTaskCategory is the resolver for the createTaskCategory field.
func (r *mutationResolver) CreateTaskCategory(ctx context.Context, input gql_model.CreateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.CreateTaskCategory(ctx, input)
}

// UpdateTaskCategory is the resolver for the updateTaskCategory field.
func (r *mutationResolver) UpdateTaskCategory(ctx context.Context, input gql_model.UpdateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.UpdateTaskCategory(ctx, input)
}

// DeleteTaskCategory is the resolver for the deleteTaskCategory field.
func (r *mutationResolver) DeleteTaskCategory(ctx context.Context, categoryID string) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.DeleteTaskCategory(ctx, categoryID)
}

// CreateTierBenefit is the resolver for the createTierBenefit field.
func (r *mutationResolver) CreateTierBenefit(ctx context.Context, input gql_model.CreateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.CreateTierBenefit(ctx, input)
}

// UpdateTierBenefit is the resolver for the updateTierBenefit field.
func (r *mutationResolver) UpdateTierBenefit(ctx context.Context, input gql_model.UpdateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.UpdateTierBenefit(ctx, input)
}

// DeleteTierBenefit is the resolver for the deleteTierBenefit field.
func (r *mutationResolver) DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.DeleteTierBenefit(ctx, tierBenefitID)
}

// AdminResetDailyTasks is the resolver for the adminResetDailyTasks field.
func (r *mutationResolver) AdminResetDailyTasks(ctx context.Context) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminResetDailyTasks(ctx)
}

// AdminResetWeeklyTasks is the resolver for the adminResetWeeklyTasks field.
func (r *mutationResolver) AdminResetWeeklyTasks(ctx context.Context) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminResetWeeklyTasks(ctx)
}

// AdminResetMonthlyTasks is the resolver for the adminResetMonthlyTasks field.
func (r *mutationResolver) AdminResetMonthlyTasks(ctx context.Context) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminResetMonthlyTasks(ctx)
}

// AdminRecalculateAllUserTiers is the resolver for the adminRecalculateAllUserTiers field.
func (r *mutationResolver) AdminRecalculateAllUserTiers(ctx context.Context) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminRecalculateAllUserTiers(ctx)
}

// AdminSeedInitialTasks is the resolver for the adminSeedInitialTasks field.
func (r *mutationResolver) AdminSeedInitialTasks(ctx context.Context) (bool, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminSeedInitialTasks(ctx)
}

// AdminCreateAgentLevel is the resolver for the adminCreateAgentLevel field.
func (r *mutationResolver) AdminCreateAgentLevel(ctx context.Context, input gql_model.CreateAgentLevelInput) (*gql_model.CreateAgentLevelResponse, error) {
	adminAgentLevelResolver := admin_resolvers.NewAdminAgentLevelResolver()
	return adminAgentLevelResolver.AdminCreateAgentLevel(ctx, input)
}

// AdminUpdateAgentLevel is the resolver for the adminUpdateAgentLevel field.
func (r *mutationResolver) AdminUpdateAgentLevel(ctx context.Context, input gql_model.UpdateAgentLevelInput) (*gql_model.UpdateAgentLevelResponse, error) {
	adminAgentLevelResolver := admin_resolvers.NewAdminAgentLevelResolver()
	return adminAgentLevelResolver.AdminUpdateAgentLevel(ctx, input)
}

// AdminDeleteAgentLevel is the resolver for the adminDeleteAgentLevel field.
func (r *mutationResolver) AdminDeleteAgentLevel(ctx context.Context, id int) (*gql_model.DeleteAgentLevelResponse, error) {
	adminAgentLevelResolver := admin_resolvers.NewAdminAgentLevelResolver()
	return adminAgentLevelResolver.AdminDeleteAgentLevel(ctx, id)
}

// AdminCreateInfiniteAgent is the resolver for the adminCreateInfiniteAgent field.
func (r *mutationResolver) AdminCreateInfiniteAgent(ctx context.Context, input gql_model.CreateInfiniteAgentInput) (*gql_model.AdminInfiniteAgentConfig, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminCreateInfiniteAgent(ctx, input)
}

// AdminUpdateInfiniteAgent is the resolver for the adminUpdateInfiniteAgent field.
func (r *mutationResolver) AdminUpdateInfiniteAgent(ctx context.Context, input gql_model.UpdateInfiniteAgentInput) (*gql_model.AdminInfiniteAgentConfig, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminUpdateInfiniteAgent(ctx, input)
}

// AdminDeleteInfiniteAgent is the resolver for the adminDeleteInfiniteAgent field.
func (r *mutationResolver) AdminDeleteInfiniteAgent(ctx context.Context, id string) (bool, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminDeleteInfiniteAgent(ctx, id)
}

// AdminUpdateAgentLevelCommissionRates is the resolver for the adminUpdateAgentLevelCommissionRates field.
func (r *mutationResolver) AdminUpdateAgentLevelCommissionRates(ctx context.Context, input gql_model.UpdateCommissionRatesInput) (*gql_model.AgentLevelWithStats, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminUpdateAgentLevelCommissionRates(ctx, input)
}

// AdminCreateReferralTreeSnapshot is the resolver for the adminCreateReferralTreeSnapshot field.
func (r *mutationResolver) AdminCreateReferralTreeSnapshot(ctx context.Context, input gql_model.CreateTreeSnapshotInput) (*gql_model.InfiniteAgentReferralTree, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminCreateReferralTreeSnapshot(ctx, input)
}

// AdminDeleteReferralTree is the resolver for the adminDeleteReferralTree field.
func (r *mutationResolver) AdminDeleteReferralTree(ctx context.Context, id string) (bool, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminDeleteReferralTree(ctx, id)
}

// AdminRecalculateAllReferralSnapshots is the resolver for the adminRecalculateAllReferralSnapshots field.
func (r *mutationResolver) AdminRecalculateAllReferralSnapshots(ctx context.Context) (*gql_model.SystemOperationResult, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminRecalculateAllReferralSnapshots(ctx)
}

// AdminRecalculateAllInfiniteAgentCommissions is the resolver for the adminRecalculateAllInfiniteAgentCommissions field.
func (r *mutationResolver) AdminRecalculateAllInfiniteAgentCommissions(ctx context.Context) (*gql_model.SystemOperationResult, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminRecalculateAllInfiniteAgentCommissions(ctx)
}

// AdminSyncReferralTreeData is the resolver for the adminSyncReferralTreeData field.
func (r *mutationResolver) AdminSyncReferralTreeData(ctx context.Context) (*gql_model.SystemOperationResult, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminSyncReferralTreeData(ctx)
}

// AdminGetAllTasks is the resolver for the adminGetAllTasks field.
func (r *queryResolver) AdminGetAllTasks(ctx context.Context) ([]*gql_model.ActivityTask, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetAllTasks(ctx)
}

// AdminGetTaskCompletionStats is the resolver for the adminGetTaskCompletionStats field.
func (r *queryResolver) AdminGetTaskCompletionStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminTaskCompletionStatsResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetTaskCompletionStats(ctx, input)
}

// AdminGetUserActivityStats is the resolver for the adminGetUserActivityStats field.
func (r *queryResolver) AdminGetUserActivityStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminUserActivityStatsResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetUserActivityStats(ctx, input)
}

// AdminGetTierDistribution is the resolver for the adminGetTierDistribution field.
func (r *queryResolver) AdminGetTierDistribution(ctx context.Context) (*gql_model.AdminTierDistributionResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetTierDistribution(ctx)
}

// AdminGetTopUsers is the resolver for the adminGetTopUsers field.
func (r *queryResolver) AdminGetTopUsers(ctx context.Context, limit *int) ([]*gql_model.UserTierInfo, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetTopUsers(ctx, limit)
}

// AdminGetInfiniteAgentByID is the resolver for the adminGetInfiniteAgentById field.
func (r *queryResolver) AdminGetInfiniteAgentByID(ctx context.Context, id string) (*gql_model.AdminInfiniteAgentConfig, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminGetInfiniteAgentById(ctx, id)
}

// AdminGetAllInfiniteAgents is the resolver for the adminGetAllInfiniteAgents field.
func (r *queryResolver) AdminGetAllInfiniteAgents(ctx context.Context, input gql_model.GetInfiniteAgentsInput) (*gql_model.AdminInfiniteAgentsResponse, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminGetAllInfiniteAgents(ctx, input)
}

// AdminGetAllAgentLevels is the resolver for the adminGetAllAgentLevels field.
func (r *queryResolver) AdminGetAllAgentLevels(ctx context.Context) ([]*gql_model.AgentLevelWithStats, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminGetAllAgentLevels(ctx)
}

// AdminSearchUsersByInvitationCode is the resolver for the adminSearchUsersByInvitationCode field.
func (r *queryResolver) AdminSearchUsersByInvitationCode(ctx context.Context, invitationCode string) ([]*gql_model.User, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminSearchUsersByInvitationCode(ctx, invitationCode)
}

// AdminGetUserReferralSnapshot is the resolver for the adminGetUserReferralSnapshot field.
func (r *queryResolver) AdminGetUserReferralSnapshot(ctx context.Context, userID string) (*gql_model.ReferralSnapshotFull, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminGetUserReferralSnapshot(ctx, userID)
}

// AdminGetUsersByAgentLevel is the resolver for the adminGetUsersByAgentLevel field.
func (r *queryResolver) AdminGetUsersByAgentLevel(ctx context.Context, input gql_model.GetUsersByLevelInput) (*gql_model.UsersByLevelResponse, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminGetUsersByAgentLevel(ctx, input)
}

// AdminGetAllReferralTrees is the resolver for the adminGetAllReferralTrees field.
func (r *queryResolver) AdminGetAllReferralTrees(ctx context.Context, input gql_model.GetReferralTreesInput) (*gql_model.ReferralTreesResponse, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminGetAllReferralTrees(ctx, input)
}

// AdminGetReferralTreeByID is the resolver for the adminGetReferralTreeById field.
func (r *queryResolver) AdminGetReferralTreeByID(ctx context.Context, id string) (*gql_model.InfiniteAgentReferralTree, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminGetReferralTreeById(ctx, id)
}

// AdminGetAgentReferralStats is the resolver for the adminGetAgentReferralStats field.
func (r *queryResolver) AdminGetAgentReferralStats(ctx context.Context, input gql_model.AdminStatsDateInput) (*gql_model.AgentReferralStats, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminGetAgentReferralStats(ctx, input)
}

// AdminGetCommissionDistributionStats is the resolver for the adminGetCommissionDistributionStats field.
func (r *queryResolver) AdminGetCommissionDistributionStats(ctx context.Context, input gql_model.AdminStatsDateInput) (*gql_model.CommissionDistributionStats, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminGetCommissionDistributionStats(ctx, input)
}

// AdminGetTopPerformingAgents is the resolver for the adminGetTopPerformingAgents field.
func (r *queryResolver) AdminGetTopPerformingAgents(ctx context.Context, input gql_model.GetTopAgentsInput) ([]*gql_model.TopAgent, error) {
	agentReferralResolver := admin_resolvers.NewAdminAgentReferralResolver()
	return agentReferralResolver.AdminGetTopPerformingAgents(ctx, input)
}

// AdminGetAllCategories is the resolver for the adminGetAllCategories field.
func (r *queryResolver) AdminGetAllCategories(ctx context.Context, input gql_model.AllTaskCategoriesInput) (*gql_model.ALlTaskCategoriesResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetAllCategories(ctx, input)
}

// AdminGetAllTierBenefits is the resolver for the adminGetAllTierBenefits field.
func (r *queryResolver) AdminGetAllTierBenefits(ctx context.Context, input gql_model.AllTierBenefitsInput) (*gql_model.ALlTierBenefitsResponse, error) {
	adminActivityCashbackResolver := admin_resolvers.NewAdminActivityCashbackResolver()
	return adminActivityCashbackResolver.AdminGetAllTierBenefits(ctx, input)
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
