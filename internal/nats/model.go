package nats

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// OrderType represents the type of order
type OrderType string

const (
	Market       OrderType = "Market"
	Limit        OrderType = "Limit"
	TPSL         OrderType = "TPSL"
	TrailingTPSL OrderType = "TrailingTPSL"
)

// TransactionType represents the type of transaction
type TransactionType string

const (
	Buy  TransactionType = "Buy"
	Sell TransactionType = "Sell"
)

// Status represents the status of an order/transaction
type Status string

const (
	StatusPending   Status = "pending"
	StatusCompleted Status = "completed"
	StatusFailed    Status = "failed"
	StatusCancelled Status = "cancelled"
	StatusCanceled  Status = "Canceled" // Alternative spelling used in some APIs
)

// AffiliateTxEventWrapper represents the wrapper structure from NATS
type AffiliateTxEventWrapper struct {
	Items []AffiliateTxEvent `json:"items"`
}

// AffiliateTxEvent represents the affiliate transaction event from NATS
type AffiliateTxEvent struct {
	ID                uuid.UUID       `json:"order_id"`
	CreatedAt         time.Time       `json:"created_at"`
	FinalizedAt       time.Time       `json:"finalized_at"`
	CompletedAt       time.Time       `json:"completed_at"`
	TransactionType   TransactionType `json:"transaction_type"`
	Type              OrderType       `json:"type"`
	ChainId           string          `json:"chain_id"`
	BaseAddress       string          `json:"base_address"`
	BaseSymbol        string          `json:"base_symbol"`
	QuoteAddress      string          `json:"quote_address"`
	QuoteSymbol       string          `json:"quote_symbol"`
	UserId            string          `json:"user_id"`
	UserAddress       string          `json:"wallet_address"` // Fixed: JSON field is "wallet_address"
	BaseAmount        decimal.Decimal `json:"base_amount"`
	QuoteAmount       decimal.Decimal `json:"quote_amount"`
	Status            Status          `json:"status"`
	Txid              string          `json:"txHash"`
	Slippage          decimal.Decimal `json:"slippage"`
	MevProtect        bool            `json:"mev_protect"`
	TotalFee          decimal.Decimal `json:"total_fee"`
	PlatformFee       decimal.Decimal `json:"platform_fee"`
	PlatformFeeAmount decimal.Decimal `json:"platform_fee_amount"`
	PlatformFeeMint   string          `json:"platform_fee_mint"`
	FeeRate           decimal.Decimal `json:"fee_rate"`

	// USD Rate fields for volume calculation
	CloseBaseUsdRate  decimal.Decimal `json:"close_base_usd_rate"`  // Base token price in USD
	CloseQuoteUsdRate decimal.Decimal `json:"close_quote_usd_rate"` // Quote token price in USD (usually SOL price)
}

// SolPriceEvent represents the SOL price event from NATS
type SolPriceEvent struct {
	Token     string          `json:"token"`     // Token address (e.g., "So11111111111111111111111111111111111111112")
	UsdPrice  decimal.Decimal `json:"usd_price"` // Price in USD
	Timestamp int64           `json:"timestamp"` // Unix timestamp
	ChainId   int             `json:"chain_id"`  // Chain ID (e.g., 501424 for Solana)
}

// GetTime returns the timestamp as time.Time
func (s *SolPriceEvent) GetTime() time.Time {
	return time.Unix(s.Timestamp, 0)
}

// GetSymbol returns a human-readable symbol for the token
func (s *SolPriceEvent) GetSymbol() string {
	// For SOL token address, return "SOL"
	if s.Token == "So11111111111111111111111111111111111111112" {
		return "SOL"
	}
	// For other tokens, return the token address (could be enhanced with a token registry)
	return s.Token
}

// DexUserWallet represents a wallet in the DexUserEvent
type DexUserWallet struct {
	ID              string    `json:"id"`
	Chain           string    `json:"chain"`
	WalletAddress   string    `json:"walletAddress"`
	WalletAccountID string    `json:"walletAccountId"`
	WalletID        string    `json:"walletId"`
	HDPath          string    `json:"hdPath"`
	Name            string    `json:"name"`
	CreatedAt       time.Time `json:"createdAt"`
}

// DexUserEvent represents the user event from NATS dex_user stream
type DexUserEvent struct {
	UserID  string          `json:"userId"`
	Wallets []DexUserWallet `json:"wallets"`
}

// NATS Stream and Subject constants
const (
	// Stream name
	AffiliateStream = "xbit-agency-affiliate"

	// Subjects
	AffiliateTxSubject = "agency.affiliate.xbit_tx"
	SolPriceSubject    = "agency.affiliate.price.501424.So11111111111111111111111111111111111111112"

	// Consumer names
	AffiliateTxConsumer = "xbit-agent-affiliate-tx"
	SolPriceConsumer    = "xbit-agent-sol-price"

	// Meme user sync
	DexUserStream         = "dex_user"
	DexUserSubject        = "dex.user.wallet.new"
	AgentSyncUserConsumer = "AgentSyncUserConsumer"

	// Dex user subscriber service
	DexUserSubscriberConsumer = "AgentDexUserSubscriberConsumer"

	// Dex hyper liquid
	DexHyperLiquidTransactionStream   = "dex_hyperliquid_transaction"
	DexHyperLiquidTransactionConsumer = "AgentHyperLiquidTxConsumer"

	DexRewardExecutorStream   = "dex_reward"
	DexRewardExecutorConsumer = "RewardExecutorConsumer"
)
