package model

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// UserTierInfo represents the user_tier_info table (separate from referral tiers)
type UserTierInfo struct {
	UserID                uuid.UUID       `gorm:"type:uuid;primary_key" json:"user_id"`
	CurrentTier           int             `gorm:"not null;default:1" json:"current_tier"`
	TotalPoints           int             `gorm:"not null;default:0" json:"total_points"`
	PointsThisMonth       int             `gorm:"not null;default:0" json:"points_this_month"`
	TradingVolumeUSD      decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"trading_volume_usd"`     // Used by LevelUpgradeTask (daily aggregation from daily_meme_volumes)
	AccumulatedVolumeUSD  decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"accumulated_volume_usd"` // Used by RealtimeVolumeSyncTask (configurable interval incremental updates)
	ActiveDaysThisMonth   int             `gorm:"default:0" json:"active_days_this_month"`
	CumulativeCashbackUSD decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"cumulative_cashback_usd"`
	ClaimableCashbackUSD  decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"claimable_cashback_usd"`
	ClaimedCashbackUSD    decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"claimed_cashback_usd"`
	LastActivityDate      *time.Time      `json:"last_activity_date"`
	TierUpgradedAt        *time.Time      `json:"tier_upgraded_at"`
	MonthlyResetAt        *time.Time      `json:"monthly_reset_at"`
	CreatedAt             time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt             time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relationships
	User        User         `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	TierBenefit *TierBenefit `gorm:"foreignKey:CurrentTier;references:TierLevel" json:"tier_benefit,omitempty"`
}

// TableName specifies the table name for UserTierInfo
func (UserTierInfo) TableName() string {
	return "user_tier_info"
}

// AddPoints adds points to the user's total and monthly points
func (uti *UserTierInfo) AddPoints(points int) {
	uti.TotalPoints += points
	uti.PointsThisMonth += points
	uti.UpdatedAt = time.Now().UTC()
}

// AddTradingVolume adds trading volume to the user's total (used by LevelUpgradeTask)
func (uti *UserTierInfo) AddTradingVolume(volume decimal.Decimal) {
	uti.TradingVolumeUSD = uti.TradingVolumeUSD.Add(volume)
	uti.UpdatedAt = time.Now().UTC()
}

// AddAccumulatedVolume adds accumulated volume to the user's total (used by RealtimeVolumeSyncTask)
func (uti *UserTierInfo) AddAccumulatedVolume(volume decimal.Decimal) {
	uti.AccumulatedVolumeUSD = uti.AccumulatedVolumeUSD.Add(volume)
	uti.UpdatedAt = time.Now().UTC()
}

// SetAccumulatedVolume sets the accumulated volume (used by RealtimeVolumeSyncTask for absolute updates)
func (uti *UserTierInfo) SetAccumulatedVolume(volume decimal.Decimal) {
	uti.AccumulatedVolumeUSD = volume
	uti.UpdatedAt = time.Now().UTC()
}

// GetEffectiveVolume returns the most up-to-date volume for Activity Cashback calculations
// Uses AccumulatedVolumeUSD (configurable interval sync) if available, otherwise falls back to TradingVolumeUSD (daily)
func (uti *UserTierInfo) GetEffectiveVolume() decimal.Decimal {
	// If accumulated volume is greater than 0, use it (real-time data from RealtimeVolumeSyncTask)
	if uti.AccumulatedVolumeUSD.GreaterThan(decimal.Zero) {
		return uti.AccumulatedVolumeUSD
	}
	// Otherwise, fall back to trading volume (daily aggregation from LevelUpgradeTask)
	return uti.TradingVolumeUSD
}

// AddCashback adds cashback to the user's claimable amount
func (uti *UserTierInfo) AddCashback(amount decimal.Decimal) {
	uti.ClaimableCashbackUSD = uti.ClaimableCashbackUSD.Add(amount)
	uti.CumulativeCashbackUSD = uti.CumulativeCashbackUSD.Add(amount)
	uti.UpdatedAt = time.Now().UTC()
}

// ClaimCashback moves claimable cashback to claimed
func (uti *UserTierInfo) ClaimCashback(amount decimal.Decimal) error {
	if uti.ClaimableCashbackUSD.LessThan(amount) {
		return errors.New("insufficient claimable cashback")
	}

	uti.ClaimableCashbackUSD = uti.ClaimableCashbackUSD.Sub(amount)
	uti.ClaimedCashbackUSD = uti.ClaimedCashbackUSD.Add(amount)
	uti.UpdatedAt = time.Now().UTC()

	return nil
}

// UpdateActivity updates the last activity date and increments active days if needed
func (uti *UserTierInfo) UpdateActivity() {
	now := time.Now().UTC()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)

	// Check if this is a new day of activity
	if uti.LastActivityDate == nil || uti.LastActivityDate.UTC().Before(today) {
		uti.ActiveDaysThisMonth++
		uti.LastActivityDate = &now
	}

	uti.UpdatedAt = now
}

// ShouldResetMonthly checks if monthly stats should be reset
func (uti *UserTierInfo) ShouldResetMonthly() bool {
	if uti.MonthlyResetAt == nil {
		return true
	}

	now := time.Now().UTC()
	resetAt := uti.MonthlyResetAt.UTC() // Ensure timezone consistency

	// Check if we're in a different month/year than the last reset
	return now.Month() != resetAt.Month() || now.Year() != resetAt.Year()
}

// IsResetNeededForCurrentMonth checks if reset is needed and not already done this month
func (uti *UserTierInfo) IsResetNeededForCurrentMonth() bool {
	if !uti.ShouldResetMonthly() {
		return false
	}

	// Additional check to prevent double reset within the same month
	now := time.Now().UTC()
	if uti.MonthlyResetAt != nil {
		resetAt := uti.MonthlyResetAt.UTC()
		// If reset was already done in current month (should not happen with correct logic, but safety check)
		if now.Month() == resetAt.Month() && now.Year() == resetAt.Year() {
			return false
		}
	}

	return true
}

// IsResetRecentlyDone checks if reset was done recently (within last 24 hours)
// This can be used to optimize performance by avoiding frequent reset checks
func (uti *UserTierInfo) IsResetRecentlyDone() bool {
	if uti.MonthlyResetAt == nil {
		return false
	}

	now := time.Now().UTC()
	resetAt := uti.MonthlyResetAt.UTC()

	// Check if reset was done in current month and within last 24 hours
	if now.Month() == resetAt.Month() && now.Year() == resetAt.Year() {
		// If reset was done within last 24 hours, consider it recent
		return now.Sub(resetAt) < 24*time.Hour
	}

	return false
}

// ResetMonthlyStats resets monthly statistics
func (uti *UserTierInfo) ResetMonthlyStats() {
	now := time.Now().UTC()
	uti.PointsThisMonth = 0
	uti.ActiveDaysThisMonth = 0
	uti.MonthlyResetAt = &now
	uti.UpdatedAt = now
}

// CanUpgradeTier checks if the user can upgrade to a higher tier
func (uti *UserTierInfo) CanUpgradeTier(nextTierMinPoints int) bool {
	return uti.TotalPoints >= nextTierMinPoints
}

// UpgradeTier upgrades the user to a new tier
func (uti *UserTierInfo) UpgradeTier(newTier int) {
	now := time.Now().UTC()
	uti.CurrentTier = newTier
	uti.TierUpgradedAt = &now
	uti.UpdatedAt = now
}

// GetPointsToNextTier calculates points needed for next tier
func (uti *UserTierInfo) GetPointsToNextTier(nextTierMinPoints int) int {
	remaining := nextTierMinPoints - uti.TotalPoints
	if remaining < 0 {
		return 0
	}
	return remaining
}
