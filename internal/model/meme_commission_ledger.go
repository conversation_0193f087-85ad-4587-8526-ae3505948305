package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type MemeCommissionLedger struct {
	ID                    uuid.UUID       `json:"id" gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid()"`
	RecipientUserID       uuid.UUID       `gorm:"type:uuid;not null;index" json:"recipient_user_id"` // Who Got Paid, This is the ID of the user who earned and will receive the commission
	SourceUserID          uuid.UUID       `gorm:"type:uuid;not null;index" json:"source_user_id"`    // Who Caused the Payment, This is the ID of the user who performed the action (the meme trade) that generated the commission. This is the downline user (the person invited by the agent).
	SourceTransactionID   string          `gorm:"type:uuid;not null" json:"source_transaction_id"`
	SourceTransactionType string          `gorm:"type:varchar(20);not null" json:"source_transaction_type"`   // Direct, Indirect, Extended
	CommissionAmount      decimal.Decimal `gorm:"type:numeric(36,18);not null" json:"commission_amount"`      // This is the raw numerical quantity of the asset that was earned. It's just a number. e.g.: 1.5, 0.39, 0.078.
	CommissionAmountSol   decimal.Decimal `gorm:"type:numeric(36,18);default:0" json:"commission_amount_sol"` // This is the raw numerical quantity of the asset that was earned. It's just a number. e.g.: 1.5, 0.39, 0.078.
	CommissionAsset       string          `gorm:"type:varchar(10);not null" json:"commission_asset"`
	Status                string          `gorm:"type:varchar(20);not null;default:'PENDING_CLAIM';index" json:"status"` // PENDING_CLAIM or CLAIMED
	CreatedAt             *time.Time      `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt             *time.Time      `json:"updated_at"`
	ClaimedAt             *time.Time      `json:"claimed_at"`
	RewardClaimResultID   *uuid.UUID      `gorm:"type:uuid;index" json:"reward_claim_result_id"`

	// Relationships
	RecipientUser     *User                    `gorm:"foreignKey:RecipientUserID;references:ID" json:"recipient_user,omitempty"`
	SourceUser        *User                    `gorm:"foreignKey:SourceUserID;references:ID" json:"source_user,omitempty"`
	RewardClaimResult *RewardClaimResultRecord `gorm:"foreignKey:RewardClaimResultID;references:ID" json:"reward_claim_result,omitempty"`
}

// TableName specifies the table name for MemeCommissionLedger
func (MemeCommissionLedger) TableName() string {
	return "meme_commission_ledger"
}
