package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// VolumeProcessingState tracks the last processed timestamp for volume calculations
// This ensures we don't double-count transactions when running incremental updates
type VolumeProcessingState struct {
	ID                    uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	ProcessorName         string    `gorm:"type:varchar(100);not null;unique;index" json:"processor_name"` // e.g., "volume_update_15min"
	LastProcessedAt       time.Time `gorm:"not null" json:"last_processed_at"`                              // Last transaction timestamp that was processed
	LastRunAt             time.Time `gorm:"not null" json:"last_run_at"`                                    // When the processor last ran
	ProcessedTransactions int64     `gorm:"default:0" json:"processed_transactions"`                        // Count of transactions processed in last run
	CreatedAt             time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt             time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName specifies the table name for VolumeProcessingState
func (VolumeProcessingState) TableName() string {
	return "volume_processing_states"
}

// UserVolumeUpdateLog tracks individual user volume updates for audit purposes
type UserVolumeUpdateLog struct {
	ID                uint            `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID            uuid.UUID       `gorm:"type:uuid;not null;index" json:"user_id"`
	ProcessorName     string          `gorm:"type:varchar(100);not null;index" json:"processor_name"`
	PreviousVolumeUSD decimal.Decimal `gorm:"type:decimal(38,2);default:0" json:"previous_volume_usd"`
	AddedVolumeUSD    decimal.Decimal `gorm:"type:decimal(38,2);default:0" json:"added_volume_usd"`
	NewVolumeUSD      decimal.Decimal `gorm:"type:decimal(38,2);default:0" json:"new_volume_usd"`
	TransactionCount  int             `gorm:"default:0" json:"transaction_count"`
	ProcessedAt       time.Time       `gorm:"not null;index" json:"processed_at"`
	CreatedAt         time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
}

// TableName specifies the table name for UserVolumeUpdateLog
func (UserVolumeUpdateLog) TableName() string {
	return "user_volume_update_logs"
}
