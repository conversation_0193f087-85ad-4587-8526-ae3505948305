package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
)

// MultilingualName represents a name in multiple languages
type MultilingualName struct {
	En string  `json:"en"`           // English (required)
	Zh *string `json:"zh,omitempty"` // Chinese Simplified
	Ja *string `json:"ja,omitempty"` // Japanese
	Hi *string `json:"hi,omitempty"` // Hindi
	Hk *string `json:"hk,omitempty"` // Chinese Traditional (Hong Kong)
	Vi *string `json:"vi,omitempty"` // Vietnamese
}

// Value implements the driver.Valuer interface for database storage
func (mn MultilingualName) Value() (driver.Value, error) {
	return json.Marshal(mn)
}

// <PERSON>an implements the sql.Scanner interface for database retrieval
func (mn *MultilingualName) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("cannot scan MultilingualName from non-string type")
	}

	return json.Unmarshal(bytes, mn)
}

// GetDisplayName returns the appropriate name based on language preference
func (mn *MultilingualName) GetDisplayName(lang string) string {
	if mn == nil {
		return ""
	}

	switch lang {
	case "zh", "zh-CN":
		if mn.Zh != nil && *mn.Zh != "" {
			return *mn.Zh
		}
	case "zh-HK", "zh-TW":
		if mn.Hk != nil && *mn.Hk != "" {
			return *mn.Hk
		}
	case "ja":
		if mn.Ja != nil && *mn.Ja != "" {
			return *mn.Ja
		}
	case "hi":
		if mn.Hi != nil && *mn.Hi != "" {
			return *mn.Hi
		}
	case "vi":
		if mn.Vi != nil && *mn.Vi != "" {
			return *mn.Vi
		}
	}

	// Default to English
	return mn.En
}

// TaskFrequency represents how often a task can be completed
type TaskFrequency string

const (
	FrequencyDaily       TaskFrequency = "DAILY"
	FrequencyOneTime     TaskFrequency = "ONE_TIME"
	FrequencyUnlimited   TaskFrequency = "UNLIMITED"
	FrequencyProgressive TaskFrequency = "PROGRESSIVE"
	FrequencyManual      TaskFrequency = "MANUAL"
)

// ResetPeriod represents when a task should be reset
type ResetPeriod string

const (
	ResetDaily   ResetPeriod = "DAILY"
	ResetWeekly  ResetPeriod = "WEEKLY"
	ResetMonthly ResetPeriod = "MONTHLY"
	ResetNever   ResetPeriod = "NEVER"
)

// VerificationMethod represents how a task is verified
type VerificationMethod string

const (
	VerificationAuto        VerificationMethod = "AUTO"
	VerificationManual      VerificationMethod = "MANUAL"
	VerificationClickVerify VerificationMethod = "CLICK_VERIFY"
)

// ConsecutiveCheckinMilestone represents a milestone in consecutive check-in tasks
type ConsecutiveCheckinMilestone struct {
	Days   int               `json:"days"`           // Number of consecutive days required
	Points int               `json:"points"`         // Points awarded when milestone is reached
	Name   *MultilingualName `json:"name,omitempty"` // Multilingual name for the milestone
}

// TaskConditions represents the conditions required to complete a task
type TaskConditions struct {
	MinTradingVolume             *float64                      `json:"min_trading_volume,omitempty"`
	RequiredTradeCount           *int                          `json:"required_trade_count,omitempty"`
	ConsecutiveDays              *int                          `json:"consecutive_days,omitempty"`
	TargetPage                   *string                       `json:"target_page,omitempty"`
	SocialMediaAction            *string                       `json:"social_media_action,omitempty"`
	ReferralCount                *int                          `json:"referral_count,omitempty"`
	ConsecutiveCheckinMilestones []ConsecutiveCheckinMilestone `json:"consecutive_checkin_milestones,omitempty"`
	CustomConditions             map[string]interface{}        `json:"custom_conditions,omitempty"`
}

// Scan implements the sql.Scanner interface for TaskConditions
func (tc *TaskConditions) Scan(value interface{}) error {
	if value == nil {
		*tc = TaskConditions{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, tc)
}

// Value implements the driver.Valuer interface for TaskConditions
func (tc TaskConditions) Value() (driver.Value, error) {
	return json.Marshal(tc)
}

// ActivityTask represents the activity_tasks table
type ActivityTask struct {
	ID          uuid.UUID         `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CategoryID  uint              `gorm:"not null" json:"category_id"`
	Name        string            `gorm:"type:varchar(100);not null" json:"name"`       // Default English name (for backward compatibility)
	NameCN      *string           `gorm:"type:varchar(100)" json:"name_cn"`             // Chinese name (for backward compatibility)
	NameVN      *string           `gorm:"type:varchar(100)" json:"name_vn"`             // Vietnamese name (for backward compatibility)
	NameData    *MultilingualName `gorm:"type:jsonb;column:name_data" json:"name_data"` // New multilingual name structure
	Description *string           `gorm:"type:text" json:"description"`

	Frequency          TaskFrequency       `gorm:"type:varchar(20);not null" json:"frequency"`
	TaskIdentifier     *TaskIdentifier     `gorm:"type:varchar(50);index" json:"task_identifier"` // New field for unique identification
	Points             int                 `gorm:"not null;default:0" json:"points"`
	MaxCompletions     *int                `json:"max_completions"` // NULL for unlimited tasks
	ResetPeriod        *ResetPeriod        `gorm:"type:varchar(20)" json:"reset_period"`
	Conditions         *TaskConditions     `gorm:"type:jsonb" json:"conditions"`
	ActionTarget       *string             `gorm:"type:varchar(255)" json:"action_target"`
	VerificationMethod *VerificationMethod `gorm:"type:varchar(50)" json:"verification_method"`
	ExternalLink       *string             `gorm:"type:varchar(500)" json:"external_link"`
	TaskIcon           *string             `gorm:"type:varchar(255)" json:"task_icon"`   // New field for task icon
	ButtonText         *string             `gorm:"type:varchar(100)" json:"button_text"` // New field for button text
	IsActive           bool                `gorm:"default:true" json:"is_active"`
	StartDate          *time.Time          `json:"start_date"`
	EndDate            *time.Time          `json:"end_date"`
	SortOrder          int                 `gorm:"default:0" json:"sort_order"`
	CreatedAt          time.Time           `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt          time.Time           `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy          *uuid.UUID          `gorm:"type:uuid" json:"created_by"`
	UpdatedBy          *uuid.UUID          `gorm:"type:uuid" json:"updated_by"`

	// Relationships
	Category     TaskCategory       `gorm:"foreignKey:CategoryID;references:ID" json:"category,omitempty"`
	UserProgress []UserTaskProgress `gorm:"foreignKey:TaskID;references:ID" json:"user_progress,omitempty"`
}

// TableName specifies the table name for ActivityTask
func (ActivityTask) TableName() string {
	return "activity_tasks"
}

// IsExpired checks if the task has expired
func (at *ActivityTask) IsExpired() bool {
	if at.EndDate == nil {
		return false
	}
	return time.Now().After(*at.EndDate)
}

// GetMultilingualName returns the multilingual name, creating it from legacy fields if needed
func (at *ActivityTask) GetMultilingualName() *MultilingualName {
	if at.NameData != nil {
		return at.NameData
	}

	// Create from legacy fields for backward compatibility
	name := &MultilingualName{
		En: at.Name,
	}

	if at.NameCN != nil && *at.NameCN != "" {
		name.Zh = at.NameCN
	}

	if at.NameVN != nil && *at.NameVN != "" {
		name.Vi = at.NameVN
	}

	return name
}

// SetMultilingualName sets the multilingual name and updates legacy fields for backward compatibility
func (at *ActivityTask) SetMultilingualName(name *MultilingualName) {
	at.NameData = name
	if name != nil {
		// Update legacy fields for backward compatibility
		at.Name = name.En
		at.NameCN = name.Zh
		at.NameVN = name.Vi
	}
}

// GetDisplayName returns the appropriate name based on language preference
func (at *ActivityTask) GetDisplayName(lang string) string {
	name := at.GetMultilingualName()
	if name == nil {
		return ""
	}
	return name.GetDisplayName(lang)
}

// IsStarted checks if the task has started
func (at *ActivityTask) IsStarted() bool {
	if at.StartDate == nil {
		return true
	}
	return time.Now().After(*at.StartDate)
}

// IsAvailable checks if the task is currently available
func (at *ActivityTask) IsAvailable() bool {
	return at.IsActive && at.IsStarted() && !at.IsExpired()
}
