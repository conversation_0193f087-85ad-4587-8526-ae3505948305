package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// InfiniteAgentConfig stores the configuration for users who are designated as "infinite agents".
type InfiniteAgentConfig struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;unique;index" json:"user_id"` // Foreign key to the User table.

	// The 'n' value from the diagram. This is the total potential commission pool rate for the infinite agent's tree.
	CommissionRateN decimal.Decimal `gorm:"type:numeric(10,6);not null" json:"commission_rate_n"`

	// --- DETAILED CALCULATION COMPONENTS ---
	// The sum of Net Fees generated by the entire tree.
	TotalNetFeeUSD decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"total_net_fee_usd"`

	// The sum of all Direct, Indirect, and Extended commissions paid out to the 3-level uplines from this tree's transactions.
	TotalStandardCommissionPaidUSD decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"total_standard_commission_paid_usd"`

	// --- FINAL RESULT ---
	// The final calculated commission for the infinite agent.
	// Formula: (TotalNetFeeUSD * PoolCommissionRateN) - TotalStandardCommissionPaidUSD
	FinalCommissionAmountUSD decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"final_commission_amount_usd"`

	// --- DETAILED FEE BREAKDOWN ---
	// Meme trading fee breakdown
	MemeTotalFeeUSD         decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"meme_total_fee_usd"`       // Total meme platform fees from affiliate_transactions.platform_fee
	MemePaidCommissionUSD   decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"meme_paid_commission_usd"` // Total meme commissions paid from meme_commission_ledger
	MemeNetFeeUSD           decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"meme_net_fee_usd"`         // Meme net fee = MemeTotalFeeUSD - MemePaidCommissionUSD
	MemeActivityCashbackUSD decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"meme_activity_cashback_usd"`

	// Contract trading fee breakdown
	ContractTotalFeeUSD       decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"contract_total_fee_usd"`       // Total contract build fees from hyper_liquid_transactions.build_fee
	ContractPaidCommissionUSD decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"contract_paid_commission_usd"` // Total contract commissions paid from commission_ledger
	ContractNetFeeUSD         decimal.Decimal `gorm:"type:numeric(38,18);not null;default:0" json:"contract_net_fee_usd"`         // Contract net fee = ContractTotalFeeUSD - ContractPaidCommissionUSD

	// --- STATUS AND METADATA ---
	Status string `gorm:"type:varchar(20);not null;default:'ACTIVE';index" json:"status"` // "ACTIVE", "INACTIVE", "SUSPENDED"

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationship
	User User `gorm:"foreignKey:UserID;references:ID" json:"user"`
}
