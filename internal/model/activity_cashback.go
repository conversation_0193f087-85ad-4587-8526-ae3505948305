package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type ActivityCashback struct {
	ID uuid.UUID `json:"id" gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid()"`

	// User Information
	UserID      uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	UserAddress string    `gorm:"type:varchar(100);not null;index" json:"user_address"`

	Status                 string `gorm:"type:varchar(20);not null;default:'PENDING_CLAIM';index" json:"status"` // PENDING_CLAIM or CLAIMED
	AffiliateTransactionID uint   `gorm:"not null;index" json:"affiliate_transaction_id"`

	// SOL Price Information
	SolPriceUSD decimal.Decimal `gorm:"type:decimal(36,18);not null" json:"sol_price_usd"` // SOL price

	// Activity Cashback Calculation
	CashbackAmountUSD decimal.Decimal `gorm:"type:decimal(36,18);not null" json:"cashback_amount_usd"` // Calculated cashback amount in USD
	CashbackAmountSOL decimal.Decimal `gorm:"type:decimal(36,18);not null" json:"cashback_amount_sol"` // cashback amount in SOL

	// Claim Reference
	RewardClaimResultID *uuid.UUID `gorm:"type:uuid;index" json:"reward_claim_result_id"`

	CreatedAt *time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	ClaimedAt *time.Time `json:"claimed_at"`

	// Relationships
	User                *User                     `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	RewardClaimResult   *RewardClaimResultRecord  `gorm:"foreignKey:RewardClaimResultID;references:ID" json:"reward_claim_result,omitempty"`
}

// TableName specifies the table name for ActivityCashback
func (ActivityCashback) TableName() string {
	return "activity_cashback"
}
