package model

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ReferralSnapshot 推荐关系快照表，记录用户的推荐统计信息
type ReferralSnapshot struct {
	// --- 基础推荐统计 ---
	UserID                  uuid.UUID       `gorm:"type:uuid;primary_key" json:"user_id"`                          // 用户ID，主键
	DirectCount             int             `gorm:"default:0" json:"direct_count"`                                 // 直接推荐用户数量
	TotalDownlineCount      int             `gorm:"default:0" json:"total_downline_count"`                         // 总下线用户数量（包含所有层级）
	TotalVolumeUSD          decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"total_volume_usd"`          // 总交易量（美元）
	TotalRewardsDistributed decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"total_rewards_distributed"` // 总奖励分发金额
	TradingUserCount        int             `gorm:"default:0" json:"trading_user_count"`                           // 有交易记录的用户数量

	// --- 个人永续合约交易统计 ---
	TotalPerpsVolumeUSD   decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"total_perps_volume_usd"`   // 个人永续合约总交易量（美元）
	TotalPerpsFees        decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_perps_fees"`        // 个人永续合约总手续费
	TotalPerpsFeesPaid    decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_perps_fees_paid"`   // 个人永续合约已支付手续费
	TotalPerpsFeesUnPaid  decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_perps_fees_unpaid"` // 个人永续合约未支付手续费
	TotalPerpsTradesCount int64           `gorm:"default:0" json:"total_perps_trades_count"`                    // 个人永续合约交易次数

	// --- 个人Meme币交易统计 ---
	TotalMemeVolumeUSD   decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"total_meme_volume_usd"`   // 个人Meme币总交易量（美元）
	TotalMemeFees        decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_meme_fees"`        // 个人Meme币总手续费
	TotalMemeFeesPaid    decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_meme_fees_paid"`   // 个人Meme币已支付手续费
	TotalMemeFeesUnPaid  decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_meme_fees_unpaid"` // 个人Meme币未支付手续费
	TotalMemeTradesCount int64           `gorm:"default:0" json:"total_meme_trades_count"`                    // 个人Meme币交易次数

	// --- 个人奖励统计 ---
	// 合约佣金相关
	TotalCommissionEarnedUSD decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_commission_earned_usd"` // 累计获得的佣金总额（美元）
	ClaimedCommissionUSD     decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"claimed_commission_usd"`      // 已领取的佣金金额
	UnclaimedCommissionUSD   decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"unclaimed_commission_usd"`    // 未领取的佣金金额
	// Meme币返现相关
	TotalCashbackEarnedUSD decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"total_cashback_earned_usd"` // 累计获得的返现总额（美元）
	ClaimedCashbackUSD     decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"claimed_cashback_usd"`      // 已领取的返现金额
	UnclaimedCashbackUSD   decimal.Decimal `gorm:"type:numeric(38,18);default:0" json:"unclaimed_cashback_usd"`    // 未领取的返现金额

	// --- 推荐关系层级 ---
	L1UplineID *uuid.UUID `gorm:"type:uuid;index" json:"l1_upline_id"` // 一级推荐人ID（直接推荐人）
	L2UplineID *uuid.UUID `gorm:"type:uuid;index" json:"l2_upline_id"` // 二级推荐人ID（间接推荐人）
	L3UplineID *uuid.UUID `gorm:"type:uuid;index" json:"l3_upline_id"` // 三级推荐人ID（延长推荐人）

	// --- 关联关系 ---
	User     User  `gorm:"foreignKey:UserID;references:ID;constraint:false" json:"user"` // 用户信息
	L1Upline *User `gorm:"foreignKey:L1UplineID" json:"l1_upline,omitempty"`             // 一级推荐人信息
	L2Upline *User `gorm:"foreignKey:L2UplineID" json:"l2_upline,omitempty"`             // 二级推荐人信息
	L3Upline *User `gorm:"foreignKey:L3UplineID" json:"l3_upline,omitempty"`             // 三级推荐人信息
}

// TableName specifies the table name for ReferralSnapshot
func (ReferralSnapshot) TableName() string {
	return "referral_snapshots"
}
