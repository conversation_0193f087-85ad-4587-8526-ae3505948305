package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type HyperLiquidTransaction struct {
	ID            uuid.UUID        `json:"id" gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid()"`
	Cloid         string           `json:"cloid" binding:"required" gorm:"column:cloid;not null;unique;"`
	UserID        *uuid.UUID       `json:"user_id" gorm:"column:user_id;type:uuid;null"`
	WalletAddress *string          `json:"wallet_address" gorm:"column:wallet_address;null"`
	Side          *string          `json:"side" gorm:"column:side;null"`             // long/short or ask/bid
	OrderType     *string          `json:"order_type" gorm:"column:order_type;null"` // Market/Limit/Trigger
	Symbol        *string          `json:"symbol" gorm:"column:symbol;null"`         // "BTC-USDT-PERP"
	IsBuy         *bool            `json:"is_buy" gorm:"column:is_buy;null"`
	Leverage      *int             `json:"leverage" gorm:"column:leverage;null"`
	Margin        *decimal.Decimal `json:"margin" gorm:"column:margin;type:decimal(20,10);null"`
	IsMarket      *bool            `json:"is_market" gorm:"column:is_market;null"`
	TriggerPx     *string          `json:"trigger_px" gorm:"column:trigger_px;null"`
	Tpsl          *string          `json:"tpsl" gorm:"column:tpsl;null"`
	Tif           *string          `json:"tif" gorm:"column:tif;null"`     // Time In Force: Alo/Ioc/Gtc
	Base          *string          `json:"base" gorm:"column:base;null"`   // "BTC"
	Quote         *string          `json:"quote" gorm:"column:quote;null"` // "USDC"
	Size          *decimal.Decimal `json:"size" gorm:"column:size;type:decimal(20,10);null"`
	Price         *decimal.Decimal `json:"price" gorm:"column:price;type:decimal(20,10);null"`
	AvgPrice      *decimal.Decimal `json:"avg_price" binding:"required" gorm:"column:avg_price;type:decimal(20,10);not null"`
	BuildFee      *decimal.Decimal `json:"build_fee" gorm:"column:build_fee;type:decimal(20,10);null"`
	TotalFee      *string          `json:"total_fee" gorm:"column:total_fee;null"`
	FeeBp         *int             `json:"fee_bp" gorm:"column:fee_bp;null"`
	BuildAddress  *string          `json:"build_address" gorm:"column:build_address;null"`
	Status        *string          `json:"status" gorm:"column:status;null"`
	OID           *int64           `json:"oid" gorm:"column:oid;null"`
	CreatedAt     *string          `json:"created_at" gorm:"column:created_at;null"`
	CreatedTime   *time.Time       `json:"created_time" gorm:"column:created_time;type:timestamp;null"`
	TotalSz       *string          `json:"total_sz" gorm:"column:total_sz;null"`
	Hash          *string          `json:"hash" gorm:"column:hash;null"`
	Asset         *string          `json:"asset" gorm:"column:asset;null"`
	Coin          string           `json:"coin" binding:"required" gorm:"column:coin;not null"`
	ReduceOnly    *bool            `json:"reduce_only" gorm:"column:reduce_only;null"`
	Grouping      *string          `json:"grouping" gorm:"column:grouping;null"`
	Operation     *string          `json:"operation" gorm:"column:operation;null"`
}
