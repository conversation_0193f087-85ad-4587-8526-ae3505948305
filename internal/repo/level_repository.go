package repo

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

type LevelRepository struct{}

func NewLevelRepository() LevelRepo {
	return &LevelRepository{}
}

// GetAgentLevels retrieves all agent levels
func (r *LevelRepository) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	var levels []model.AgentLevel
	err := global.GVA_DB.WithContext(ctx).Find(&levels).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get agent levels: %w", err)
	}
	return levels, nil
}

// GetAgentLevelByID retrieves a specific agent level by ID
func (r *LevelRepository) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	var level model.AgentLevel
	err := global.GVA_DB.WithContext(ctx).Where("id = ?", id).First(&level).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get agent level by ID %d: %w", id, err)
	}
	return &level, nil
}

// CreateAgentLevel creates a new agent level
func (r *LevelRepository) CreateAgentLevel(ctx context.Context, level *model.AgentLevel) error {
	err := global.GVA_DB.WithContext(ctx).Create(level).Error
	if err != nil {
		return fmt.Errorf("failed to create agent level: %w", err)
	}
	return nil
}

// UpdateAgentLevel updates an agent level's commission rates and meme fee rebate
func (r *LevelRepository) UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) error {
	err := global.GVA_DB.WithContext(ctx).Save(level).Error
	if err != nil {
		return fmt.Errorf("failed to update agent level: %w", err)
	}
	return nil
}

// DeleteAgentLevel deletes an agent level by ID
func (r *LevelRepository) DeleteAgentLevel(ctx context.Context, id uint) error {
	err := global.GVA_DB.WithContext(ctx).Delete(&model.AgentLevel{}, id).Error
	if err != nil {
		return fmt.Errorf("failed to delete agent level: %w", err)
	}
	return nil
}

// CountUsersByAgentLevel counts how many users are using a specific agent level
func (r *LevelRepository) CountUsersByAgentLevel(ctx context.Context, levelID uint) (int64, error) {
	var count int64
	err := global.GVA_DB.WithContext(ctx).Model(&model.User{}).Where("agent_level_id = ?", levelID).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count users by agent level: %w", err)
	}
	return count, nil
}
