package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// ActivityCashbackRepositoryInterface defines the interface for activity cashback operations
type ActivityCashbackRepositoryInterface interface {
	GetPendingCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.ActivityCashback, error)
	GetClaimedCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.ActivityCashback, error)
	UpdateCashbackStatusToClaimed(ctx context.Context, userID uuid.UUID, cashbackIDs []uuid.UUID) error
	UpdateCashbackWithClaimID(ctx context.Context, cashbackIDs []uuid.UUID, claimID uuid.UUID) error
	GetCashbackByID(ctx context.Context, cashbackID uuid.UUID) (*model.ActivityCashback, error)
}

type ActivityCashbackRepository struct {
	db *gorm.DB
}

func NewActivityCashbackRepository() ActivityCashbackRepositoryInterface {
	return &ActivityCashbackRepository{
		db: global.GVA_DB,
	}
}

// GetPendingCashbacksByUserID gets all pending cashbacks for a user
func (r *ActivityCashbackRepository) GetPendingCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.ActivityCashback, error) {
	var cashbacks []model.ActivityCashback
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND status = ?", userID, "PENDING_CLAIM").
		Find(&cashbacks).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get pending cashbacks: %w", err)
	}

	return cashbacks, nil
}

// GetClaimedCashbacksByUserID gets all claimed cashbacks for a user
func (r *ActivityCashbackRepository) GetClaimedCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.ActivityCashback, error) {
	var cashbacks []model.ActivityCashback
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND status = ?", userID, "CLAIMED").
		Find(&cashbacks).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get claimed cashbacks: %w", err)
	}

	return cashbacks, nil
}

// UpdateCashbackStatusToClaimed updates the status of specified cashbacks to CLAIMED
func (r *ActivityCashbackRepository) UpdateCashbackStatusToClaimed(ctx context.Context, userID uuid.UUID, cashbackIDs []uuid.UUID) error {
	now := time.Now()
	err := r.db.WithContext(ctx).
		Model(&model.ActivityCashback{}).
		Where("user_id = ? AND id IN ? AND status = ?", userID, cashbackIDs, "PENDING_CLAIM").
		Updates(map[string]interface{}{
			"status":     "CLAIMED",
			"claimed_at": &now,
			"updated_at": &now,
		}).Error

	if err != nil {
		return fmt.Errorf("failed to update cashback status: %w", err)
	}

	return nil
}

// GetCashbackByID gets a specific cashback by ID
func (r *ActivityCashbackRepository) GetCashbackByID(ctx context.Context, cashbackID uuid.UUID) (*model.ActivityCashback, error) {
	var cashback model.ActivityCashback
	err := r.db.WithContext(ctx).
		Where("id = ?", cashbackID).
		First(&cashback).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get cashback by ID: %w", err)
	}

	return &cashback, nil
}

// UpdateCashbackWithClaimID updates activity cashback records with claim ID
func (r *ActivityCashbackRepository) UpdateCashbackWithClaimID(ctx context.Context, cashbackIDs []uuid.UUID, claimID uuid.UUID) error {
	now := time.Now()
	err := r.db.WithContext(ctx).
		Model(&model.ActivityCashback{}).
		Where("id IN ? AND status = ?", cashbackIDs, "PENDING_CLAIM").
		Updates(map[string]interface{}{
			"activity_cashback_claim_id": claimID,
			"status":                     "CLAIMED",
			"claimed_at":                 &now,
			"updated_at":                 &now,
		}).Error

	if err != nil {
		return fmt.Errorf("failed to update cashback with claim ID: %w", err)
	}

	return nil
}
