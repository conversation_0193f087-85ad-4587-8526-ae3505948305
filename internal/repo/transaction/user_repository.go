package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// UserRepositoryInterface defines the interface for user operations in transaction context
type UserRepositoryInterface interface {
	GetDirectReferrals(ctx context.Context, userID uuid.UUID) ([]model.User, error)
	GetAllDownlineUsers(ctx context.Context, userID uuid.UUID, maxDepth int) ([]uuid.UUID, error)
	GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error)
	GetInvitationCountByUserIDAndPeriod(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) (int, error)
	GetUserWalletAddress(ctx context.Context, userID uuid.UUID) (string, error)
	GetUserPerpetualWalletAddress(ctx context.Context, userID uuid.UUID) (string, error)
}

type UserRepository struct {
	db *gorm.DB
}

func NewUserRepository() UserRepositoryInterface {
	return &UserRepository{
		db: global.GVA_DB,
	}
}

// GetDirectReferrals gets all direct referrals (level 1) for a user
func (r *UserRepository) GetDirectReferrals(ctx context.Context, userID uuid.UUID) ([]model.User, error) {
	var referrals []model.Referral
	var users []model.User

	// Get all direct referrals
	err := r.db.WithContext(ctx).
		Where("referrer_id = ? AND depth = 1", userID).
		Find(&referrals).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get direct referrals: %w", err)
	}

	if len(referrals) == 0 {
		return users, nil
	}

	// Extract user IDs from referrals
	var userIDs []uuid.UUID
	for _, referral := range referrals {
		userIDs = append(userIDs, referral.UserID)
	}

	// Get user details
	err = r.db.WithContext(ctx).
		Where("id IN ?", userIDs).
		Find(&users).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user details: %w", err)
	}

	return users, nil
}

// GetAllDownlineUsers gets all downline users up to specified depth
func (r *UserRepository) GetAllDownlineUsers(ctx context.Context, userID uuid.UUID, maxDepth int) ([]uuid.UUID, error) {
	var referrals []model.Referral
	var userIDs []uuid.UUID

	// Get all referrals up to maxDepth
	err := r.db.WithContext(ctx).
		Where("referrer_id = ? AND depth <= ?", userID, maxDepth).
		Find(&referrals).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get downline users: %w", err)
	}

	// Extract user IDs from referrals
	for _, referral := range referrals {
		userIDs = append(userIDs, referral.UserID)
	}

	return userIDs, nil
}

func (r *UserRepository) GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		Preload("Referrer.Referral").
		First(&referral, "user_id = ?", userID).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}

// GetInvitationCountByUserIDAndPeriod gets the invitation count for a user within a time period
func (r *UserRepository) GetInvitationCountByUserIDAndPeriod(ctx context.Context, userID uuid.UUID, startTime, endTime time.Time) (int, error) {
	var count int64

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	err := r.db.WithContext(ctx).
		Model(&model.Referral{}).
		Where("referrer_id = ? AND depth = 1", userID).
		Where("created_at::timestamp >= ?::timestamp AND created_at::timestamp < ?::timestamp", startTimeUTC, endTimeUTC).
		Count(&count).Error

	if err != nil {
		return 0, fmt.Errorf("failed to get invitation count by period: %w", err)
	}

	return int(count), nil
}

// GetUserWalletAddress gets the wallet address for a user
func (r *UserRepository) GetUserWalletAddress(ctx context.Context, userID uuid.UUID) (string, error) {
	var walletAddress string
	err := r.db.WithContext(ctx).
		Model(&model.UserWallet{}).
		Select("wallet_address").
		Where("user_id = ?", userID).
		Limit(1).
		Pluck("wallet_address", &walletAddress).Error

	if err != nil {
		return "", fmt.Errorf("failed to get user wallet address: %w", err)
	}

	if walletAddress == "" {
		return "", fmt.Errorf("no wallet address found for user %s", userID.String())
	}

	return walletAddress, nil
}

func (r *UserRepository) GetUserPerpetualWalletAddress(ctx context.Context, userID uuid.UUID) (string, error) {
	var walletAddress string
	err := r.db.WithContext(ctx).
		Model(&model.UserWallet{}).
		Select("wallet_address").
		Where("user_id = ? AND chain = ?", userID, model.ChainArb).
		Limit(1).
		Pluck("wallet_address", &walletAddress).Error

	if err != nil {
		return "", fmt.Errorf("failed to get user wallet address: %w", err)
	}

	if walletAddress == "" {
		return "", fmt.Errorf("no wallet address found for user %s", userID.String())
	}

	return walletAddress, nil
}
