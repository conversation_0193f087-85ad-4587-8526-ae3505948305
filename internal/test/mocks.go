package test

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// MockLevelRepo is a mock implementation of repo.LevelRepo
type MockLevelRepo struct {
	mock.Mock
}

func (m *MockLevelRepo) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.AgentLevel), args.Error(1)
}

func (m *MockLevelRepo) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.AgentLevel), args.Error(1)
}

func (m *MockLevelRepo) CreateAgentLevel(ctx context.Context, level *model.AgentLevel) error {
	args := m.Called(ctx, level)
	return args.Error(0)
}

func (m *MockLevelRepo) UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) error {
	args := m.Called(ctx, level)
	return args.Error(0)
}

func (m *MockLevelRepo) DeleteAgentLevel(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockLevelRepo) CountUsersByAgentLevel(ctx context.Context, levelID uint) (int64, error) {
	args := m.Called(ctx, levelID)
	return args.Get(0).(int64), args.Error(1)
}

// MockInvitationRepo is a mock implementation of repo.InvitationRepo
type MockInvitationRepo struct {
	mock.Mock
}

func (m *MockInvitationRepo) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.ReferralSnapshot), args.Error(1)
}

func (m *MockInvitationRepo) UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID, walletType, invitationCode, email string) (*model.User, error) {
	args := m.Called(ctx, userID, chain, name, walletAddress, walletID, walletAccountID, walletType, invitationCode, email)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationRepo) CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error {
	args := m.Called(ctx, referrerID, userID)
	return args.Error(0)
}

func (m *MockInvitationRepo) GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error) {
	args := m.Called(ctx, invitationCode)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationRepo) GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationRepo) GetTradingUserCount(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (int, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Int(0), args.Error(1)
}

func (m *MockInvitationRepo) GetExtendedInvitedUserCount(ctx context.Context, userID uuid.UUID) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

func (m *MockInvitationRepo) GetInvitedAddresses(ctx context.Context, userID uuid.UUID) ([]string, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockInvitationRepo) GetMemeTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Get(0).(float64), args.Error(1)
}

func (m *MockInvitationRepo) GetContractTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Get(0).(float64), args.Error(1)
}

// MockAffiliateRepository is a mock implementation of repo.AffiliateRepositoryInterface
type MockAffiliateRepository struct {
	mock.Mock
}

func (m *MockAffiliateRepository) CreateAffiliateTransaction(ctx context.Context, tx *model.AffiliateTransaction) error {
	args := m.Called(ctx, tx)
	return args.Error(0)
}

func (m *MockAffiliateRepository) GetAffiliateTransactionByTxHash(ctx context.Context, txHash string) (*model.AffiliateTransaction, error) {
	args := m.Called(ctx, txHash)
	return args.Get(0).(*model.AffiliateTransaction), args.Error(1)
}

func (m *MockAffiliateRepository) UpdateAffiliateTransaction(ctx context.Context, tx *model.AffiliateTransaction) error {
	args := m.Called(ctx, tx)
	return args.Error(0)
}

// TODO: Add transaction-related mocks when needed
// These require the transaction package import which has formatting issues

/*
// MockUserRepository is a mock implementation of transaction.UserRepositoryInterface
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) GetUserByID(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetUserByWalletAddress(ctx context.Context, walletAddress string) (*model.User, error) {
	args := m.Called(ctx, walletAddress)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) CreateUser(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) UpdateUser(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

// MockCommissionLedgerRepository is a mock implementation of transaction.CommissionLedgerRepositoryInterface
type MockCommissionLedgerRepository struct {
	mock.Mock
}

func (m *MockCommissionLedgerRepository) CreateCommissionEntry(ctx context.Context, entry *model.CommissionLedger) error {
	args := m.Called(ctx, entry)
	return args.Error(0)
}

func (m *MockCommissionLedgerRepository) GetCommissionsByUserID(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) ([]model.CommissionLedger, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Get(0).([]model.CommissionLedger), args.Error(1)
}

func (m *MockCommissionLedgerRepository) GetTotalCommissionByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(decimal.Decimal), args.Error(1)
}
*/
