package initializer

import (
	"fmt"
	"log"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
)

var messagePubHandler mqtt.MessageHandler = func(client mqtt.Client, msg mqtt.Message) {
	fmt.Printf("Received message: %s from topic: %s\n", msg.Payload(), msg.Topic())
}

var connectHandler mqtt.OnConnectHandler = func(client mqtt.Client) {
	fmt.Println("Connected")
}

var connectLostHandler mqtt.ConnectionLostHandler = func(client mqtt.Client, err error) {
	fmt.Printf("Connect lost: %v", err)
}

var mqttClient mqtt.Client
var onceInitMqtt sync.Once

func InitMqtt() {
	onceInitMqtt.Do(func() {
		opts := mqtt.NewClientOptions()
		opts.AddBroker(fmt.Sprintf("%s://%s:%s/mqtt", global.GVA_CONFIG.Mqtt.Protocol, global.GVA_CONFIG.Mqtt.Host, global.GVA_CONFIG.Mqtt.Port))
		opts.SetClientID(fmt.Sprintf("xbit-indexer-%d", time.Now().UnixMicro()))
		opts.SetUsername(global.GVA_CONFIG.Mqtt.Username)
		opts.SetPassword(global.GVA_CONFIG.Mqtt.Password)
		opts.SetDefaultPublishHandler(messagePubHandler)
		opts.OnConnect = connectHandler
		opts.OnConnectionLost = connectLostHandler

		opts.SetAutoReconnect(true)
		opts.SetConnectRetry(true)
		opts.SetConnectRetryInterval(1 * time.Second) // wait 1s before trying again
		opts.SetMaxReconnectInterval(15 * time.Second)
		opts.SetKeepAlive(60 * time.Second)   // send PINGREQ every 60s
		opts.SetPingTimeout(10 * time.Second) // wait 10s for PINGRESP
		opts.SetConnectTimeout(30 * time.Second)

		mqttClient = mqtt.NewClient(opts)
		if token := mqttClient.Connect(); token.Wait() && token.Error() != nil {
			log.Printf("failed to connect to mqtt server: %v", token.Error())
		}

		log.Printf("mqtt client initialized and connected")
		global.GVA_MQTT = &mqttClient
	})

}

func GetMqttClient() *mqtt.Client {
	if mqttClient == nil {
		log.Printf("mqtt client not initialized")
	}
	return &mqttClient
}
