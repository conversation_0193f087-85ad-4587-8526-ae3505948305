package infinite

/*
ReferralTreeNode Table Field Explanations:

Level Field:
- Represents the hierarchical level in the referral tree structure
- Level 1: Root user (the top of the referral tree)
- Level 2: Direct referrals (users directly referred by the root user)
- Level 3: Indirect referrals (users referred by Level 2 users)
- Level N: Nth level referrals (users referred by Level N-1 users)
- Formula: Level = Depth + 1 (where Depth is the distance from root)

Position Field:
- Represents the processing order/position of the user in the current snapshot
- Used for maintaining consistent ordering when querying tree nodes
- Helps in sorting and displaying users in a predictable sequence
- Starts from 1 and increments for each user processed
- Useful for pagination and consistent data retrieval

Example Tree Structure:
Root User (Level 1, Position 1)
├── Direct Referral A (Level 2, Position 2)
│   ├── Indirect Referral A1 (Level 3, Position 3)
│   └── Indirect Referral A2 (Level 3, Position 4)
└── Direct Referral B (Level 2, Position 5)
    └── Indirect Referral B1 (Level 3, Position 6)
*/

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ReferralTreeSnapshotTask Referral tree snapshot task
type ReferralTreeSnapshotTask struct{}

// NewReferralTreeSnapshotTask Create new referral tree snapshot task instance
// Proxy logic steps:
// 1. Get all referral relationships with depth = 1, where referral_id is the root user
// 2. Get subordinate users with depth = 2 or higher
// 3. Recursively query all subordinate users
// 4. Calculate the tree structure
// 5. Save tree and node snapshots
func NewReferralTreeSnapshotTask() *ReferralTreeSnapshotTask {
	return &ReferralTreeSnapshotTask{}
}

// CreateReferralTreeSnapshots Create referral tree snapshots
// Execute daily at midnight, create referral tree snapshots for each root user
func (t *ReferralTreeSnapshotTask) CreateReferralTreeSnapshots() {
	global.GVA_LOG.Info("Starting referral tree snapshot task")

	// Get yesterday's date as snapshot date
	snapshotDate := time.Now().UTC().AddDate(0, 0, -1)
	// snapshotDate := time.Now().UTC()
	snapshotDateStr := snapshotDate.Format("2006-01-02")

	global.GVA_LOG.Info("Snapshot date", zap.String("date", snapshotDateStr))

	// Get all valid root users with data validation
	rootUsers, err := t.getValidRootUsers()
	if err != nil {
		global.GVA_LOG.Error("Failed to get valid root users", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Found valid root users", zap.Int("root_user_count", len(rootUsers)))

	if len(rootUsers) == 0 {
		global.GVA_LOG.Info("No valid root users found, skipping snapshot task")
		return
	}

	processedCount := 0
	errorCount := 0
	skippedCount := 0
	updatedCount := 0

	for _, rootUser := range rootUsers {
		// Check if snapshot already exists for today
		existingSnapshot, err := t.getExistingSnapshot(rootUser.ID, snapshotDate)
		if err != nil {
			global.GVA_LOG.Error("Failed to check existing snapshot",
				zap.String("root_user_id", rootUser.ID.String()),
				zap.Error(err))
			errorCount++
			continue
		}

		if existingSnapshot != nil {
			// Check if tree structure has changed
			if t.hasTreeStructureChanged(rootUser.ID, existingSnapshot) {
				global.GVA_LOG.Info("Tree structure changed, updating snapshot",
					zap.String("root_user_id", rootUser.ID.String()),
					zap.String("date", snapshotDateStr))

				if err := t.updateReferralTreeSnapshot(rootUser.ID, snapshotDate, existingSnapshot); err != nil {
					global.GVA_LOG.Error("Failed to update referral tree snapshot",
						zap.String("root_user_id", rootUser.ID.String()),
						zap.Error(err))
					errorCount++
				} else {
					updatedCount++
				}
			} else {
				global.GVA_LOG.Info("Tree structure unchanged, skipping",
					zap.String("root_user_id", rootUser.ID.String()),
					zap.String("date", snapshotDateStr))
				skippedCount++
			}
			continue
		}

		// Relax validation conditions to allow processing trees with minor data issues
		// Only perform basic tree structure validation, don't skip trees with depth inconsistencies
		if !t.validateTreeStructure(rootUser.ID) {
			global.GVA_LOG.Warn("Invalid tree structure detected, skipping",
				zap.String("root_user_id", rootUser.ID.String()))
			skippedCount++
			continue
		}

		// Check for depth inconsistencies but don't skip, only log warnings
		if t.hasDepthInconsistencies(rootUser.ID) {
			global.GVA_LOG.Warn("Depth inconsistencies detected in tree, but continuing with processing",
				zap.String("root_user_id", rootUser.ID.String()))
		}

		if err := t.processReferralTreeSnapshot(rootUser.ID, snapshotDate); err != nil {
			global.GVA_LOG.Error("Failed to process referral tree snapshot",
				zap.String("root_user_id", rootUser.ID.String()),
				zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	global.GVA_LOG.Info("Referral tree snapshot task completed",
		zap.String("date", snapshotDateStr),
		zap.Int("total_trees", len(rootUsers)),
		zap.Int("processed_count", processedCount),
		zap.Int("updated_count", updatedCount),
		zap.Int("skipped_count", skippedCount),
		zap.Int("error_count", errorCount))
}

// getRootUsers Get all root users (users without referrers)
func (t *ReferralTreeSnapshotTask) getRootUsers() ([]model.User, error) {
	var rootUsers []model.User

	// select * from referrals where depth = 1; referral_id root user
	query := `
		SELECT DISTINCT u.*
		FROM users u
		JOIN referrals r ON u.id = r.referrer_id
		WHERE r.depth = 1
		ORDER BY u.id;
	`

	err := global.GVA_DB.Raw(query).Scan(&rootUsers).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query root users: %w", err)
	}

	return rootUsers, nil
}

// getUsersByDepth Get users at specific depth
func (t *ReferralTreeSnapshotTask) getUsersByDepth(depth int) ([]model.User, error) {
	var users []model.User

	query := `
		SELECT DISTINCT u.*
		FROM users u
		JOIN referrals r ON u.id = r.user_id
		WHERE r.depth = ?
		ORDER BY u.id;
	`

	err := global.GVA_DB.Raw(query, depth).Scan(&users).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query users at depth %d: %w", depth, err)
	}

	return users, nil
}

// getUsersByDepthRange Get users within a depth range
func (t *ReferralTreeSnapshotTask) getUsersByDepthRange(minDepth, maxDepth int) ([]model.User, error) {
	var users []model.User

	query := `
		SELECT DISTINCT u.*
		FROM users u
		JOIN referrals r ON u.id = r.user_id
		WHERE r.depth >= ? AND r.depth <= ?
		ORDER BY r.depth, u.id;
	`

	err := global.GVA_DB.Raw(query, minDepth, maxDepth).Scan(&users).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query users in depth range %d-%d: %w", minDepth, maxDepth, err)
	}

	return users, nil
}

// processReferralTreeSnapshot Process single referral tree snapshot
func (t *ReferralTreeSnapshotTask) processReferralTreeSnapshot(rootUserID uuid.UUID, snapshotDate time.Time) error {
	return t.ProcessReferralTreeSnapshotWithInfiniteAgent(rootUserID, snapshotDate, nil)
}

// ProcessReferralTreeSnapshotWithInfiniteAgent Process single referral tree snapshot (with infinite agent information)
func (t *ReferralTreeSnapshotTask) ProcessReferralTreeSnapshotWithInfiniteAgent(rootUserID uuid.UUID, snapshotDate time.Time, infiniteAgentUserID *uuid.UUID) error {
	// Delete existing table data
	// Then create new table data
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// Delete existing snapshot data within transaction
		err := t.deleteExistingSnapshotDataInTransaction(tx, rootUserID, snapshotDate)
		if err != nil {
			return fmt.Errorf("failed to delete existing snapshot data: %w", err)
		}

		// Get all users in tree
		treeUsers, err := t.getAllUsersInTree(rootUserID)
		if err != nil {
			return fmt.Errorf("failed to get users in tree: %w", err)
		}

		// Calculate tree structure information
		treeInfo, err := t.calculateTreeInfo(rootUserID, treeUsers)
		if err != nil {
			return fmt.Errorf("failed to calculate tree info: %w", err)
		}

		// Check if contains infinite agent
		hasInfiniteAgent := infiniteAgentUserID != nil
		if !hasInfiniteAgent {
			// If no infinite agent specified, check if tree contains infinite agent
			hasInfiniteAgent, infiniteAgentUserID = t.checkInfiniteAgentInTree(treeUsers)
		}

		// Create tree snapshot
		treeSnapshot := &model.ReferralTreeSnapshot{
			RootUserID:          rootUserID,
			SnapshotDate:        snapshotDate,
			TotalNodes:          treeInfo.TotalNodes,
			MaxDepth:            treeInfo.MaxDepth,
			DirectCount:         treeInfo.DirectCount,
			ActiveUsers:         treeInfo.ActiveUsers,
			TradingUsers:        treeInfo.TradingUsers,
			InfiniteAgentUserID: infiniteAgentUserID,
			HasInfiniteAgent:    hasInfiniteAgent,
			Description:         fmt.Sprintf("Referral Tree Snapshot - Root User: %s, Date: %s", rootUserID.String(), snapshotDate.Format("2006-01-02")),
			IsValid:             true,
		}

		// Save tree snapshot within transaction
		err = tx.Create(treeSnapshot).Error
		if err != nil {
			return fmt.Errorf("failed to save tree snapshot: %w", err)
		}

		// Create tree node snapshots within transaction
		err = t.createTreeNodesInTransaction(tx, treeSnapshot.ID, treeUsers)
		if err != nil {
			return fmt.Errorf("failed to create tree node snapshots: %w", err)
		}

		global.GVA_LOG.Debug("Referral tree snapshot processing completed",
			zap.String("root_user_id", rootUserID.String()),
			zap.Time("snapshot_date", snapshotDate),
			zap.Int("total_nodes", treeInfo.TotalNodes),
			zap.Int("max_depth", treeInfo.MaxDepth),
			zap.Int("direct_count", treeInfo.DirectCount),
			zap.Bool("has_infinite_agent", hasInfiniteAgent))

		return nil
	})
}

// getExistingSnapshot Check if snapshot already exists
func (t *ReferralTreeSnapshotTask) getExistingSnapshot(rootUserID uuid.UUID, snapshotDate time.Time) (*model.ReferralTreeSnapshot, error) {
	var snapshot model.ReferralTreeSnapshot

	err := global.GVA_DB.Where("root_user_id = ? AND DATE(snapshot_date) = DATE(?)", rootUserID, snapshotDate).
		First(&snapshot).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &snapshot, nil
}

// deleteExistingSnapshotData Delete existing snapshot data for the given root user and date
func (t *ReferralTreeSnapshotTask) deleteExistingSnapshotData(rootUserID uuid.UUID, snapshotDate time.Time) error {
	// Check if existing data exists
	hasExistingData, err := t.checkExistingSnapshotData(rootUserID, snapshotDate)
	if err != nil {
		return fmt.Errorf("failed to check existing data: %w", err)
	}

	if !hasExistingData {
		global.GVA_LOG.Debug("No existing snapshot data found, proceeding with creation",
			zap.String("root_user_id", rootUserID.String()),
			zap.Time("snapshot_date", snapshotDate))
		return nil
	}

	global.GVA_LOG.Info("Found existing snapshot data, deleting before creating new data",
		zap.String("root_user_id", rootUserID.String()),
		zap.Time("snapshot_date", snapshotDate))

	// Delete existing tree node snapshots first (due to foreign key constraints)
	var deletedNodes int64
	err = global.GVA_DB.Exec(`
		DELETE FROM referral_tree_nodes
		WHERE tree_snapshot_id IN (
			SELECT id FROM referral_tree_snapshots
			WHERE root_user_id = ? AND DATE(snapshot_date) = DATE(?)
		)
	`, rootUserID, snapshotDate).Error

	if err != nil {
		return fmt.Errorf("failed to delete existing tree nodes: %w", err)
	}

	// Delete existing tree snapshots
	var deletedSnapshots int64
	err = global.GVA_DB.Exec(`
		DELETE FROM referral_tree_snapshots
		WHERE root_user_id = ? AND DATE(snapshot_date) = DATE(?)
	`, rootUserID, snapshotDate).Error

	if err != nil {
		return fmt.Errorf("failed to delete existing tree snapshots: %w", err)
	}

	global.GVA_LOG.Info("Successfully deleted existing snapshot data",
		zap.String("root_user_id", rootUserID.String()),
		zap.Time("snapshot_date", snapshotDate),
		zap.Int64("deleted_snapshots", deletedSnapshots),
		zap.Int64("deleted_nodes", deletedNodes))

	return nil
}

// deleteExistingSnapshotDataInTransaction Delete existing snapshot data within a transaction
func (t *ReferralTreeSnapshotTask) deleteExistingSnapshotDataInTransaction(tx *gorm.DB, rootUserID uuid.UUID, snapshotDate time.Time) error {
	// Check if existing data exists
	hasExistingData, err := t.checkExistingSnapshotData(rootUserID, snapshotDate)
	if err != nil {
		return fmt.Errorf("failed to check existing data: %w", err)
	}

	if !hasExistingData {
		global.GVA_LOG.Debug("No existing snapshot data found, proceeding with creation",
			zap.String("root_user_id", rootUserID.String()),
			zap.Time("snapshot_date", snapshotDate))
		return nil
	}

	global.GVA_LOG.Info("Found existing snapshot data, deleting before creating new data",
		zap.String("root_user_id", rootUserID.String()),
		zap.Time("snapshot_date", snapshotDate))

	// Delete existing tree node snapshots first (due to foreign key constraints)
	err = tx.Exec(`
		DELETE FROM referral_tree_nodes
		WHERE tree_snapshot_id IN (
			SELECT id FROM referral_tree_snapshots
			WHERE root_user_id = ? AND DATE(snapshot_date) = DATE(?)
		)
	`, rootUserID, snapshotDate).Error

	if err != nil {
		return fmt.Errorf("failed to delete existing tree nodes: %w", err)
	}

	// Delete existing tree snapshots
	err = tx.Exec(`
		DELETE FROM referral_tree_snapshots
		WHERE root_user_id = ? AND DATE(snapshot_date) = DATE(?)
	`, rootUserID, snapshotDate).Error

	if err != nil {
		return fmt.Errorf("failed to delete existing tree snapshots: %w", err)
	}

	global.GVA_LOG.Info("Successfully deleted existing snapshot data within transaction",
		zap.String("root_user_id", rootUserID.String()),
		zap.Time("snapshot_date", snapshotDate))

	return nil
}

// deleteAllSnapshotData Delete all snapshot data (for cleanup purposes)
func (t *ReferralTreeSnapshotTask) deleteAllSnapshotData() error {
	// Delete all tree node snapshots first (due to foreign key constraints)
	err := global.GVA_DB.Exec("DELETE FROM referral_tree_nodes").Error
	if err != nil {
		return fmt.Errorf("failed to delete all tree nodes: %w", err)
	}

	// Delete all tree snapshots
	err = global.GVA_DB.Exec("DELETE FROM referral_tree_snapshots").Error
	if err != nil {
		return fmt.Errorf("failed to delete all tree snapshots: %w", err)
	}

	global.GVA_LOG.Info("Deleted all snapshot data")

	return nil
}

// checkExistingSnapshotData Check if snapshot data exists for the given root user and date
func (t *ReferralTreeSnapshotTask) checkExistingSnapshotData(rootUserID uuid.UUID, snapshotDate time.Time) (bool, error) {
	var count int64

	err := global.GVA_DB.Model(&model.ReferralTreeSnapshot{}).
		Where("root_user_id = ? AND DATE(snapshot_date) = DATE(?)", rootUserID, snapshotDate).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check existing snapshot data: %w", err)
	}

	return count > 0, nil
}

// getAllUsersInTree Get all users in tree with deduplication
func (t *ReferralTreeSnapshotTask) getAllUsersInTree(rootUserID uuid.UUID) ([]model.User, error) {
	var users []model.User

	// Modified query logic to handle duplicate referral relationship issues
	// For users with multiple referral relationships, only select the earliest created referral relationship
	query := `
		WITH RECURSIVE descendants AS (
			-- root user
			SELECT id, email, invitation_code, created_at, updated_at, deleted_at,
				   agent_level_id, level_grace_period_started_at, level_upgraded_at,
				   first_transaction_at, 0 as depth, NULL::uuid as parent_id, NULL::uuid as referrer_id,
				   ARRAY[id] as path
			FROM users WHERE id = ?
			UNION ALL
			-- Recursively query all subordinate users with cycle detection
			-- 对于有多个推荐关系的用户，选择最早创建的记录
			SELECT u.id, u.email, u.invitation_code, u.created_at, u.updated_at, u.deleted_at,
				   u.agent_level_id, u.level_grace_period_started_at, u.level_upgraded_at,
				   u.first_transaction_at, d.depth + 1, d.id, r.referrer_id,
				   d.path || u.id
			FROM users u
			JOIN (
				-- Select the earliest referral relationship record for each user
				SELECT DISTINCT ON (user_id) user_id, referrer_id, depth, created_at
				FROM referrals
				WHERE user_id IS NOT NULL
				ORDER BY user_id, created_at ASC
			) r ON u.id = r.user_id
			JOIN descendants d ON d.id = r.referrer_id
			WHERE NOT (u.id = ANY(d.path))  -- Prevent cycles
		)
		SELECT DISTINCT id, email, invitation_code, created_at, updated_at, deleted_at,
		       agent_level_id, level_grace_period_started_at, level_upgraded_at,
		       first_transaction_at, depth, parent_id, referrer_id
		FROM descendants
		ORDER BY depth, id;
	`

	err := global.GVA_DB.Raw(query, rootUserID).Scan(&users).Error
	if err != nil {
		return nil, fmt.Errorf("failed to recursively query users: %w", err)
	}

	// Additional validation: ensure no duplicate users in the result
	userMap := make(map[uuid.UUID]bool)
	var validUsers []model.User

	for _, user := range users {
		if !userMap[user.ID] {
			userMap[user.ID] = true
			validUsers = append(validUsers, user)
		} else {
			global.GVA_LOG.Warn("Duplicate user found in tree, removing",
				zap.String("user_id", user.ID.String()),
				zap.String("root_user_id", rootUserID.String()))
		}
	}

	global.GVA_LOG.Debug("Retrieved users from tree",
		zap.String("root_user_id", rootUserID.String()),
		zap.Int("total_users", len(validUsers)))

	return validUsers, nil
}

// TreeInfo Tree information structure
type TreeInfo struct {
	TotalNodes   int
	MaxDepth     int
	DirectCount  int
	ActiveUsers  int
	TradingUsers int
}

// calculateTreeInfo Calculate tree structure information
func (t *ReferralTreeSnapshotTask) calculateTreeInfo(rootUserID uuid.UUID, users []model.User) (*TreeInfo, error) {
	if len(users) == 0 {
		return &TreeInfo{}, nil
	}

	info := &TreeInfo{
		TotalNodes: len(users),
	}

	maxDepth := 0
	directCount := 0
	activeUsers := 0
	tradingUsers := 0

	for _, user := range users {
		depth, err := t.calculateUserDepth(rootUserID, user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate user depth", zap.String("user_id", user.ID.String()), zap.Error(err))
			continue
		}

		// Statistics of directly recommended users (users with a depth of 1)
		if depth == 1 {
			directCount++
		}

		// Update maximum depth
		if depth > maxDepth {
			maxDepth = depth
		}

		// Users with transaction records
		if user.FirstTransactionAt != nil {
			activeUsers++
			tradingUsers++
		}
	}

	info.MaxDepth = maxDepth
	info.DirectCount = directCount
	info.ActiveUsers = activeUsers
	info.TradingUsers = tradingUsers

	return info, nil
}

// calculateUserDepth Calculate user depth in tree
func (t *ReferralTreeSnapshotTask) calculateUserDepth(rootUserID, userID uuid.UUID) (int, error) {
	if rootUserID == userID {
		return 0, nil
	}

	var depth int
	// Modified query logic to handle duplicate referral relationship issues
	// For users with multiple referral relationships, select the earliest created record to calculate depth
	query := `
		WITH RECURSIVE path AS (
			SELECT user_id, referrer_id, 1 as depth
			FROM (
				-- Select the earliest referral relationship record for each user
				SELECT DISTINCT ON (user_id) user_id, referrer_id, created_at
				FROM referrals
				WHERE user_id = ?
				ORDER BY user_id, created_at ASC
			) r
			UNION ALL
			SELECT r.user_id, r.referrer_id, p.depth + 1
			FROM (
				-- Select the earliest referral relationship record for each user
				SELECT DISTINCT ON (user_id) user_id, referrer_id, created_at
				FROM referrals
				WHERE user_id IS NOT NULL
				ORDER BY user_id, created_at ASC
			) r
			JOIN path p ON r.user_id = p.referrer_id
			WHERE p.referrer_id != ?
		)
		SELECT MAX(depth) FROM path;
	`

	err := global.GVA_DB.Raw(query, userID, rootUserID).Scan(&depth).Error
	if err != nil {
		// If query fails, try using simplified depth calculation
		global.GVA_LOG.Warn("Failed to calculate user depth with recursive query, using fallback",
			zap.String("user_id", userID.String()),
			zap.String("root_user_id", rootUserID.String()),
			zap.Error(err))

		// Use fallback method: directly query user's referral relationship
		var referral model.Referral
		err = global.GVA_DB.Where("user_id = ?", userID).Order("created_at ASC").First(&referral).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// User has no referral relationship, might be root user
				return 0, nil
			}
			return 0, err
		}

		// Recursively calculate referrer's depth
		referrerDepth, err := t.calculateUserDepth(rootUserID, *referral.ReferrerID)
		if err != nil {
			return 0, err
		}

		return referrerDepth + 1, nil
	}

	return depth, nil
}

// createTreeNodes Create tree node snapshots
func (t *ReferralTreeSnapshotTask) createTreeNodes(treeSnapshotID uint, users []model.User) error {
	var treeNodes []model.ReferralTreeNode

	for i, user := range users {
		// Calculating user depth
		depth, err := t.calculateUserDepth(users[0].ID, user.ID) // The first user is the root user
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate user depth", zap.String("user_id", user.ID.String()), zap.Error(err))
			continue
		}

		var referrerID *uuid.UUID
		if depth > 0 {
			referrerID, err = t.getReferrerID(user.ID)
			if err != nil {
				global.GVA_LOG.Warn("Failed to get referrer ID", zap.String("user_id", user.ID.String()), zap.Error(err))
			}
		}

		isTrading := user.FirstTransactionAt != nil

		// Create tree node snapshot
		// Level: Represents the hierarchical level in the referral tree (starts from 1)
		//        Level 1 = root user, Level 2 = direct referrals, Level 3 = indirect referrals, etc.
		// Position: Represents the order/position of the user in the current processing sequence
		//           Used for maintaining consistent ordering when querying tree nodes
		treeNode := model.ReferralTreeNode{
			TreeSnapshotID: treeSnapshotID,
			UserID:         user.ID,
			ParentUserID:   referrerID,
			ReferrerID:     referrerID,
			Depth:          depth,
			Level:          depth + 1, // Level starts from 1 (root = level 1)
			Position:       i + 1,     // Position starts from 1 (processing order)
			IsActive:       true,
			IsTrading:      isTrading,
			AgentLevelID:   user.AgentLevelID,
		}

		treeNodes = append(treeNodes, treeNode)
	}

	if len(treeNodes) > 0 {
		err := global.GVA_DB.CreateInBatches(treeNodes, 100).Error
		if err != nil {
			return fmt.Errorf("failed to batch create tree nodes: %w", err)
		}
	}

	return nil
}

// createTreeNodesInTransaction Create tree node snapshots within a transaction
func (t *ReferralTreeSnapshotTask) createTreeNodesInTransaction(tx *gorm.DB, treeSnapshotID uint, users []model.User) error {
	var treeNodes []model.ReferralTreeNode

	for i, user := range users {
		// Calculating user depth
		depth, err := t.calculateUserDepth(users[0].ID, user.ID) // The first user is the root user
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate user depth", zap.String("user_id", user.ID.String()), zap.Error(err))
			continue
		}

		// Get referrer ID
		var referrerID *uuid.UUID
		if depth > 0 {
			referrerID, err = t.getReferrerID(user.ID)
			if err != nil {
				global.GVA_LOG.Warn("Failed to get referrer ID", zap.String("user_id", user.ID.String()), zap.Error(err))
			}
		}

		// Determine if trading user
		isTrading := user.FirstTransactionAt != nil

		// Create tree node snapshot
		// Level: Represents the hierarchical level in the referral tree (starts from 1)
		//        Level 1 = root user, Level 2 = direct referrals, Level 3 = indirect referrals, etc.
		// Position: Represents the order/position of the user in the current processing sequence
		//           Used for maintaining consistent ordering when querying tree nodes
		treeNode := model.ReferralTreeNode{
			TreeSnapshotID: treeSnapshotID,
			UserID:         user.ID,
			ParentUserID:   referrerID,
			ReferrerID:     referrerID,
			Depth:          depth,
			Level:          depth + 1, // Level starts from 1 (root = level 1)
			Position:       i + 1,     // Position starts from 1 (processing order)
			IsActive:       true,
			IsTrading:      isTrading,
			AgentLevelID:   user.AgentLevelID,
		}

		treeNodes = append(treeNodes, treeNode)
	}

	// Batch save tree node snapshots within transaction
	if len(treeNodes) > 0 {
		err := tx.CreateInBatches(treeNodes, 100).Error
		if err != nil {
			return fmt.Errorf("failed to batch create tree nodes: %w", err)
		}
	}

	return nil
}

// getReferrerID Get user's referrer ID
func (t *ReferralTreeSnapshotTask) getReferrerID(userID uuid.UUID) (*uuid.UUID, error) {
	var referral model.Referral

	// For users with multiple referral relationships, select the earliest created record
	err := global.GVA_DB.Where("user_id = ?", userID).Order("created_at ASC").First(&referral).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return referral.ReferrerID, nil
}

// checkInfiniteAgentInTree Check if tree contains infinite agent
func (t *ReferralTreeSnapshotTask) checkInfiniteAgentInTree(users []model.User) (bool, *uuid.UUID) {
	for _, user := range users {
		// Check if user has active infinite agent configuration
		var config model.InfiniteAgentConfig
		err := global.GVA_DB.Where("user_id = ? AND status = ?", user.ID, "ACTIVE").First(&config).Error
		if err == nil {
			// Found active infinite agent configuration
			return true, &user.ID
		}
	}
	return false, nil
}

// getValidRootUsers Get all valid root users with data validation
func (t *ReferralTreeSnapshotTask) getValidRootUsers() ([]model.User, error) {
	var rootUsers []model.User

	// Enhanced query to get valid root users with data validation
	// Filter out users with duplicate referral relationships, only keep users with single referral relationships
	query := `
		WITH valid_referrals AS (
			-- Get referrals with valid depth and no circular references
			SELECT DISTINCT r1.user_id, r1.referrer_id, r1.depth
			FROM referrals r1
			WHERE r1.depth = 1
			AND r1.user_id != r1.referrer_id  -- Prevent self-referral
			AND r1.referrer_id IS NOT NULL
			AND NOT EXISTS (
				-- Check for circular references
				SELECT 1 FROM referrals r2
				WHERE r2.user_id = r1.referrer_id
				AND r2.referrer_id = r1.user_id
			)
		),
		-- Filter out users with duplicate referral relationships, only keep users with single referral relationships
		users_with_single_referral AS (
			SELECT user_id, referrer_id, depth
			FROM valid_referrals vr
			WHERE (
				SELECT COUNT(*) FROM referrals r
				WHERE r.user_id = vr.user_id
			) = 1
		),
		root_candidates AS (
			-- Get users who are referrers but not referred by others
			SELECT DISTINCT u.*
			FROM users u
			JOIN users_with_single_referral usr ON u.id = usr.referrer_id
			WHERE NOT EXISTS (
				SELECT 1 FROM referrals r
				WHERE r.user_id = u.id AND r.depth > 0
			)
		)
		SELECT * FROM root_candidates
		ORDER BY id;
	`

	err := global.GVA_DB.Raw(query).Scan(&rootUsers).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query valid root users: %w", err)
	}

	global.GVA_LOG.Info("Found valid root users after filtering duplicates",
		zap.Int("count", len(rootUsers)))

	return rootUsers, nil
}

// validateTreeStructure Validate tree structure for a given root user
func (t *ReferralTreeSnapshotTask) validateTreeStructure(rootUserID uuid.UUID) bool {
	// Check for circular references
	circularQuery := `
		WITH RECURSIVE tree_path AS (
			SELECT user_id, referrer_id, ARRAY[user_id] as path
			FROM referrals
			WHERE referrer_id = ?
			UNION ALL
			SELECT r.user_id, r.referrer_id, tp.path || r.user_id
			FROM referrals r
			JOIN tree_path tp ON r.referrer_id = tp.user_id
			WHERE NOT (r.user_id = ANY(tp.path))  -- Prevent cycles
		)
		SELECT COUNT(*) FROM tree_path;
	`

	var count int64
	err := global.GVA_DB.Raw(circularQuery, rootUserID).Scan(&count).Error
	if err != nil {
		global.GVA_LOG.Warn("Failed to validate tree structure",
			zap.String("root_user_id", rootUserID.String()),
			zap.Error(err))
		return false
	}

	// Check if tree has reasonable size (not too small, not too large)
	if count < 1 || count > 10000 {
		global.GVA_LOG.Warn("Tree size out of reasonable range",
			zap.String("root_user_id", rootUserID.String()),
			zap.Int64("tree_size", count))
		return false
	}

	// Check for orphaned nodes (users without proper referrer chain)
	var orphanedCount int64
	orphanedQuery := `
		SELECT COUNT(*) FROM referrals r1
		WHERE r1.referrer_id IN (
			SELECT user_id FROM referrals WHERE referrer_id = ?
		)
		AND NOT EXISTS (
			SELECT 1 FROM referrals r2
			WHERE r2.user_id = r1.referrer_id
		)
	`

	err = global.GVA_DB.Raw(orphanedQuery, rootUserID).Scan(&orphanedCount).Error
	if err != nil {
		global.GVA_LOG.Warn("Failed to check orphaned nodes",
			zap.String("root_user_id", rootUserID.String()),
			zap.Error(err))
		return false
	}

	if orphanedCount > 0 {
		global.GVA_LOG.Warn("Found orphaned nodes in tree",
			zap.String("root_user_id", rootUserID.String()),
			zap.Int64("orphaned_count", orphanedCount))
		return false
	}

	return true
}

// hasTreeStructureChanged Check if tree structure has changed since last snapshot
func (t *ReferralTreeSnapshotTask) hasTreeStructureChanged(rootUserID uuid.UUID, existingSnapshot *model.ReferralTreeSnapshot) bool {
	// Get current tree structure
	treeUsers, err := t.getAllUsersInTree(rootUserID)
	if err != nil {
		global.GVA_LOG.Warn("Failed to get current tree structure",
			zap.String("root_user_id", rootUserID.String()),
			zap.Error(err))
		return true // Assume changed if we can't check
	}

	// Compare basic metrics
	if len(treeUsers) != existingSnapshot.TotalNodes {
		return true
	}

	// Check if any new users were added
	var newUserCount int64
	err = global.GVA_DB.Model(&model.ReferralTreeNode{}).
		Where("tree_snapshot_id = ?", existingSnapshot.ID).
		Count(&newUserCount).Error

	if err != nil {
		return true
	}

	if int(newUserCount) != len(treeUsers) {
		return true
	}

	return false
}

// updateReferralTreeSnapshot Update existing referral tree snapshot
func (t *ReferralTreeSnapshotTask) updateReferralTreeSnapshot(rootUserID uuid.UUID, snapshotDate time.Time, existingSnapshot *model.ReferralTreeSnapshot) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// Delete existing tree node snapshots first
		err := tx.Exec("DELETE FROM referral_tree_nodes WHERE tree_snapshot_id = ?", existingSnapshot.ID).Error
		if err != nil {
			return fmt.Errorf("failed to delete existing tree nodes: %w", err)
		}

		// Get updated tree structure
		treeUsers, err := t.getAllUsersInTree(rootUserID)
		if err != nil {
			return fmt.Errorf("failed to get updated tree structure: %w", err)
		}

		// Recalculate tree info
		treeInfo, err := t.calculateTreeInfo(rootUserID, treeUsers)
		if err != nil {
			return fmt.Errorf("failed to recalculate tree info: %w", err)
		}

		// Check infinite agent
		hasInfiniteAgent, infiniteAgentUserID := t.checkInfiniteAgentInTree(treeUsers)

		// Update tree snapshot
		updates := map[string]interface{}{
			"total_nodes":            treeInfo.TotalNodes,
			"max_depth":              treeInfo.MaxDepth,
			"direct_count":           treeInfo.DirectCount,
			"active_users":           treeInfo.ActiveUsers,
			"trading_users":          treeInfo.TradingUsers,
			"infinite_agent_user_id": infiniteAgentUserID,
			"has_infinite_agent":     hasInfiniteAgent,
			"description":            fmt.Sprintf("Referral Tree Snapshot Updated - Root User: %s, Date: %s", rootUserID.String(), snapshotDate.Format("2006-01-02")),
			"updated_at":             time.Now().UTC(),
		}

		err = tx.Model(existingSnapshot).Updates(updates).Error
		if err != nil {
			return fmt.Errorf("failed to update tree snapshot: %w", err)
		}

		// Create new tree node snapshots
		err = t.createTreeNodesInTransaction(tx, existingSnapshot.ID, treeUsers)
		if err != nil {
			return fmt.Errorf("failed to create updated tree node snapshots: %w", err)
		}

		global.GVA_LOG.Info("Successfully updated referral tree snapshot",
			zap.String("root_user_id", rootUserID.String()),
			zap.Time("snapshot_date", snapshotDate),
			zap.Int("total_nodes", treeInfo.TotalNodes))

		return nil
	})
}

// hasDepthInconsistencies Check if there are depth inconsistencies in the tree
func (t *ReferralTreeSnapshotTask) hasDepthInconsistencies(rootUserID uuid.UUID) bool {
	var inconsistentCount int64

	// Check for depth inconsistencies using the provided SQL logic
	query := `
		SELECT COUNT(*)
		FROM referrals r1
		JOIN referrals r2 ON r1.user_id = r2.referrer_id
		WHERE r1.depth + 1 != r2.depth
		AND r1.referrer_id = ?
	`

	err := global.GVA_DB.Raw(query, rootUserID).Scan(&inconsistentCount).Error
	if err != nil {
		global.GVA_LOG.Warn("Failed to check depth inconsistencies",
			zap.String("root_user_id", rootUserID.String()),
			zap.Error(err))
		return true // Assume inconsistent if we can't check
	}

	if inconsistentCount > 0 {
		global.GVA_LOG.Warn("Found depth inconsistencies in tree",
			zap.String("root_user_id", rootUserID.String()),
			zap.Int64("inconsistent_count", inconsistentCount))
		return true
	}

	// Additional check: verify depth progression from root
	var depthProgressionCount int64
	depthQuery := `
		WITH RECURSIVE tree_depths AS (
			-- Start with root user at depth 0
			SELECT ? as user_id, 0 as expected_depth
			UNION ALL
			-- Recursively calculate expected depths
			SELECT r.user_id, td.expected_depth + 1
			FROM referrals r
			JOIN tree_depths td ON r.referrer_id = td.user_id
		)
		SELECT COUNT(*)
		FROM tree_depths td
		JOIN referrals r ON td.user_id = r.user_id
		WHERE td.expected_depth != r.depth
	`

	err = global.GVA_DB.Raw(depthQuery, rootUserID).Scan(&depthProgressionCount).Error
	if err != nil {
		global.GVA_LOG.Warn("Failed to check depth progression",
			zap.String("root_user_id", rootUserID.String()),
			zap.Error(err))
		return true // Assume inconsistent if we can't check
	}

	if depthProgressionCount > 0 {
		global.GVA_LOG.Warn("Found depth progression inconsistencies in tree",
			zap.String("root_user_id", rootUserID.String()),
			zap.Int64("progression_inconsistent_count", depthProgressionCount))
		return true
	}

	return false
}

// For testing purposes, add some public methods
// GetValidRootUsers Get valid root users (for testing)
func (t *ReferralTreeSnapshotTask) GetValidRootUsers() ([]model.User, error) {
	return t.getValidRootUsers()
}

// GetAllUsersInTree Get all users in tree (for testing)
func (t *ReferralTreeSnapshotTask) GetAllUsersInTree(rootUserID uuid.UUID) ([]model.User, error) {
	return t.getAllUsersInTree(rootUserID)
}

// CalculateTreeInfo Calculate tree info (for testing)
func (t *ReferralTreeSnapshotTask) CalculateTreeInfo(rootUserID uuid.UUID, users []model.User) (*TreeInfo, error) {
	return t.calculateTreeInfo(rootUserID, users)
}
