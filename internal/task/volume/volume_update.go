package volume

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	ProcessorName = "realtime_volume_sync"
)

// RealtimeVolumeSyncTask handles incremental volume updates at configurable intervals
type RealtimeVolumeSyncTask struct{}

// NewRealtimeVolumeSyncTask creates a new RealtimeVolumeSyncTask instance
func NewRealtimeVolumeSyncTask() *RealtimeVolumeSyncTask {
	return &RealtimeVolumeSyncTask{}
}

// SyncTradingVolumes processes new transactions and updates user trading volumes incrementally
// This runs at configurable intervals to provide near real-time volume updates without overloading the system
func (t *RealtimeVolumeSyncTask) SyncTradingVolumes() {
	global.GVA_LOG.Info("Starting realtime volume sync task")

	// Get or create processing state
	state, err := t.getOrCreateProcessingState()
	if err != nil {
		global.GVA_LOG.Error("Failed to get processing state", zap.Error(err))
		return
	}

	// Calculate time range for processing
	now := time.Now().UTC()
	fromTime := state.LastProcessedAt
	toTime := now

	global.GVA_LOG.Info("Processing transactions in time range",
		zap.Time("from", fromTime),
		zap.Time("to", toTime),
		zap.String("processor", ProcessorName))

	// Process new transactions
	processedCount, err := t.processNewTransactions(fromTime, toTime)
	if err != nil {
		global.GVA_LOG.Error("Failed to process new transactions", zap.Error(err))
		return
	}

	// Update processing state
	err = t.updateProcessingState(state, toTime, processedCount)
	if err != nil {
		global.GVA_LOG.Error("Failed to update processing state", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Realtime volume sync completed",
		zap.Int64("processed_transactions", processedCount),
		zap.Time("last_processed_at", toTime),
		zap.String("processor", ProcessorName))
}

// getOrCreateProcessingState gets existing processing state or creates a new one
func (t *RealtimeVolumeSyncTask) getOrCreateProcessingState() (*model.VolumeProcessingState, error) {
	var state model.VolumeProcessingState

	err := global.GVA_DB.Where("processor_name = ?", ProcessorName).First(&state).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create new state starting from 24 hours ago to avoid processing all historical data
			startTime := time.Now().UTC().Add(-24 * time.Hour)
			state = model.VolumeProcessingState{
				ProcessorName:         ProcessorName,
				LastProcessedAt:       startTime,
				LastRunAt:             time.Now().UTC(),
				ProcessedTransactions: 0,
			}

			err = global.GVA_DB.Create(&state).Error
			if err != nil {
				return nil, fmt.Errorf("failed to create processing state: %w", err)
			}

			global.GVA_LOG.Info("Created new processing state",
				zap.String("processor", ProcessorName),
				zap.Time("start_time", startTime))
		} else {
			return nil, fmt.Errorf("failed to get processing state: %w", err)
		}
	}

	return &state, nil
}

// processNewTransactions processes transactions created after the last processed timestamp
func (t *RealtimeVolumeSyncTask) processNewTransactions(fromTime, toTime time.Time) (int64, error) {
	// Get new completed transactions grouped by user
	var userVolumeResults []struct {
		UserID           uuid.UUID       `json:"user_id"`
		TotalVolume      decimal.Decimal `json:"total_volume"`
		TransactionCount int64           `json:"transaction_count"`
	}

	err := global.GVA_DB.Model(&model.AffiliateTransaction{}).
		Select(`
			user_id,
			COALESCE(SUM(volume_usd), 0) as total_volume,
			COUNT(*) as transaction_count
		`).
		Where("created_at > ? AND created_at <= ?", fromTime, toTime).
		Where("status = ?", "Completed").
		Where("user_id IS NOT NULL").
		Group("user_id").
		Scan(&userVolumeResults).Error

	if err != nil {
		return 0, fmt.Errorf("failed to get new transactions: %w", err)
	}

	if len(userVolumeResults) == 0 {
		global.GVA_LOG.Info("No new transactions to process",
			zap.Time("from", fromTime),
			zap.Time("to", toTime))
		return 0, nil
	}

	global.GVA_LOG.Info("Found new transactions to process",
		zap.Int("user_count", len(userVolumeResults)),
		zap.Time("from", fromTime),
		zap.Time("to", toTime))

	// Process each user's volume update
	var totalProcessedTransactions int64
	processedAt := time.Now().UTC()

	for _, result := range userVolumeResults {
		err := t.updateUserTradingVolume(result.UserID, result.TotalVolume, int(result.TransactionCount), processedAt)
		if err != nil {
			global.GVA_LOG.Error("Failed to update user trading volume",
				zap.String("user_id", result.UserID.String()),
				zap.String("volume", result.TotalVolume.String()),
				zap.Error(err))
			continue
		}

		// After updating volume, check and complete accumulated trading tasks
		t.checkAccumulatedTradingTasks(result.UserID)

		totalProcessedTransactions += result.TransactionCount
	}

	return totalProcessedTransactions, nil
}

// updateUserTradingVolume updates a single user's accumulated volume in user_tier_info
// This method updates the accumulated_volume_usd column which is separate from trading_volume_usd
// to avoid conflicts with LevelUpgradeTask
func (t *RealtimeVolumeSyncTask) updateUserTradingVolume(userID uuid.UUID, addedVolume decimal.Decimal, transactionCount int, processedAt time.Time) error {
	// Start transaction for atomic update
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// Get current user tier info
		var userTierInfo model.UserTierInfo
		err := tx.Where("user_id = ?", userID).First(&userTierInfo).Error

		previousVolume := decimal.Zero
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// Create new user tier info
				userTierInfo = model.UserTierInfo{
					UserID:                userID,
					CurrentTier:           1,
					TotalPoints:           0,
					PointsThisMonth:       0,
					TradingVolumeUSD:      decimal.Zero, // Keep separate from accumulated volume
					AccumulatedVolumeUSD:  addedVolume,  // Use accumulated volume for RealtimeVolumeSyncTask
					ActiveDaysThisMonth:   0,
					CumulativeCashbackUSD: decimal.Zero,
					ClaimableCashbackUSD:  decimal.Zero,
					ClaimedCashbackUSD:    decimal.Zero,
					CreatedAt:             time.Now().UTC(),
					UpdatedAt:             time.Now().UTC(),
				}

				err = tx.Create(&userTierInfo).Error
				if err != nil {
					return fmt.Errorf("failed to create user tier info: %w", err)
				}
			} else {
				return fmt.Errorf("failed to get user tier info: %w", err)
			}
		} else {
			// Update existing record - use accumulated volume instead of trading volume
			previousVolume = userTierInfo.AccumulatedVolumeUSD
			userTierInfo.AccumulatedVolumeUSD = userTierInfo.AccumulatedVolumeUSD.Add(addedVolume)
			userTierInfo.UpdatedAt = time.Now().UTC()

			err = tx.Save(&userTierInfo).Error
			if err != nil {
				return fmt.Errorf("failed to update user tier info: %w", err)
			}
		}

		// Log the update for audit purposes
		updateLog := model.UserVolumeUpdateLog{
			UserID:            userID,
			ProcessorName:     ProcessorName,
			PreviousVolumeUSD: previousVolume,
			AddedVolumeUSD:    addedVolume,
			NewVolumeUSD:      userTierInfo.AccumulatedVolumeUSD, // Use accumulated volume
			TransactionCount:  transactionCount,
			ProcessedAt:       processedAt,
		}

		err = tx.Create(&updateLog).Error
		if err != nil {
			return fmt.Errorf("failed to create update log: %w", err)
		}

		global.GVA_LOG.Info("Updated user accumulated volume",
			zap.String("user_id", userID.String()),
			zap.String("previous_accumulated_volume", previousVolume.String()),
			zap.String("added_volume", addedVolume.String()),
			zap.String("new_accumulated_volume", userTierInfo.AccumulatedVolumeUSD.String()),
			zap.Int("transaction_count", transactionCount))

		return nil
	})
}

// updateProcessingState updates the processing state with new timestamp and count
func (t *RealtimeVolumeSyncTask) updateProcessingState(state *model.VolumeProcessingState, lastProcessedAt time.Time, processedCount int64) error {
	state.LastProcessedAt = lastProcessedAt
	state.LastRunAt = time.Now().UTC()
	state.ProcessedTransactions = processedCount
	state.UpdatedAt = time.Now().UTC()

	return global.GVA_DB.Save(state).Error
}

// checkAccumulatedTradingTasks checks and completes accumulated trading tasks for a user
// This is called after volume updates to ensure tasks are completed immediately when milestones are reached
func (t *RealtimeVolumeSyncTask) checkAccumulatedTradingTasks(userID uuid.UUID) {
	// Create activity cashback service instance
	activityService := activity_cashback.NewActivityCashbackService()

	// Create task manager to handle accumulated trading tasks
	taskManager := activity_cashback.NewTaskManager(activityService)

	ctx := context.Background()

	// Check and update all accumulated trading tasks for this user
	if err := taskManager.CheckAndUpdateAllAccumulatedTradingTasks(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to check accumulated trading tasks after volume sync",
			zap.String("user_id", userID.String()),
			zap.String("processor", ProcessorName),
			zap.Error(err))
	} else {
		global.GVA_LOG.Info("Successfully checked accumulated trading tasks after volume sync",
			zap.String("user_id", userID.String()),
			zap.String("processor", ProcessorName))
	}
}
