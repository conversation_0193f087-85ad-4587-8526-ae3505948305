-- Modify "commission_ledger" table
ALTER TABLE "public"."commission_ledger" ADD COLUMN "reward_claim_result_id" uuid NULL, ADD CONSTRAINT "fk_commission_ledger_reward_claim_result" FOREIGN KEY ("reward_claim_result_id") REFERENCES "public"."reward_claim_results" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION;
-- Create index "idx_commission_ledger_reward_claim_result_id" to table: "commission_ledger"
CREATE INDEX "idx_commission_ledger_reward_claim_result_id" ON "public"."commission_ledger" ("reward_claim_result_id");
