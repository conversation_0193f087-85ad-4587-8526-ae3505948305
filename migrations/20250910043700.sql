-- Modify "meme_commission_ledger" table
ALTER TABLE "public"."meme_commission_ledger" ADD COLUMN "commission_amount_sol" numeric(36,18) NULL DEFAULT 0, ADD COLUMN "reward_claim_result_id" uuid NULL, ADD CONSTRAINT "fk_meme_commission_ledger_reward_claim_result" FOREIGN KEY ("reward_claim_result_id") REFERENCES "public"."reward_claim_results" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION;
-- Create index "idx_meme_commission_ledger_reward_claim_result_id" to table: "meme_commission_ledger"
CREATE INDEX "idx_meme_commission_ledger_reward_claim_result_id" ON "public"."meme_commission_ledger" ("reward_claim_result_id");
