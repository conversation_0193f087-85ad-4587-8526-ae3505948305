-- Create "volume_processing_states" table
CREATE TABLE "public"."volume_processing_states" (
  "id" bigserial NOT NULL,
  "processor_name" character varying(100) NOT NULL,
  "last_processed_at" timestamptz NOT NULL,
  "last_run_at" timestamptz NOT NULL,
  "processed_transactions" bigint NULL DEFAULT 0,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "uni_volume_processing_states_processor_name" UNIQUE ("processor_name")
);
-- Create index "idx_volume_processing_states_processor_name" to table: "volume_processing_states"
CREATE INDEX "idx_volume_processing_states_processor_name" ON "public"."volume_processing_states" ("processor_name");
-- Create "user_volume_update_logs" table
CREATE TABLE "public"."user_volume_update_logs" (
  "id" bigserial NOT NULL,
  "user_id" uuid NOT NULL,
  "processor_name" character varying(100) NOT NULL,
  "previous_volume_usd" numeric(38,2) NULL DEFAULT 0,
  "added_volume_usd" numeric(38,2) NULL DEFAULT 0,
  "new_volume_usd" numeric(38,2) NULL DEFAULT 0,
  "transaction_count" bigint NULL DEFAULT 0,
  "processed_at" timestamptz NOT NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_user_volume_update_logs_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_user_volume_update_logs_processed_at" to table: "user_volume_update_logs"
CREATE INDEX "idx_user_volume_update_logs_processed_at" ON "public"."user_volume_update_logs" ("processed_at");
-- Create index "idx_user_volume_update_logs_processor_name" to table: "user_volume_update_logs"
CREATE INDEX "idx_user_volume_update_logs_processor_name" ON "public"."user_volume_update_logs" ("processor_name");
-- Create index "idx_user_volume_update_logs_user_id" to table: "user_volume_update_logs"
CREATE INDEX "idx_user_volume_update_logs_user_id" ON "public"."user_volume_update_logs" ("user_id");
