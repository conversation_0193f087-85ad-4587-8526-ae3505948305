-- Modify "activity_cashback" table
ALTER TABLE "public"."activity_cashback" DROP COLUMN "activity_cashback_claim_id", ADD COLUMN "reward_claim_result_id" uuid NULL, ADD CONSTRAINT "fk_activity_cashback_reward_claim_result" FOREIGN KEY ("reward_claim_result_id") REFERENCES "public"."reward_claim_results" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION;
-- <PERSON><PERSON> index "idx_activity_cashback_reward_claim_result_id" to table: "activity_cashback"
CREATE INDEX "idx_activity_cashback_reward_claim_result_id" ON "public"."activity_cashback" ("reward_claim_result_id");
