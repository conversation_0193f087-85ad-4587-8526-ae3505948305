-- Modify "activity_cashback" table
ALTER TABLE "public"."activity_cashback" ADD COLUMN "activity_cashback_claim_id" uuid NULL, ADD CONSTRAINT "fk_activity_cashback_activity_cashback_claim" FOREIGN KEY ("activity_cashback_claim_id") REFERENCES "public"."activity_cashback_claims" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION;
-- Create index "idx_activity_cashback_activity_cashback_claim_id" to table: "activity_cashback"
CREATE INDEX "idx_activity_cashback_activity_cashback_claim_id" ON "public"."activity_cashback" ("activity_cashback_claim_id");
